import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ref, query, orderByChild, limitToFirst, get, startAt, endAt } from 'firebase/database';
import { rtdb } from '@/lib/firebase';
import { Link, useLocation } from 'react-router-dom';

interface PopupData {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  active: boolean;
  startDate: Date;
  endDate: Date;
  buttonText: string;
  buttonUrl: string;
  createdAt?: number;
}

const EventPopup: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [popup, setPopup] = useState<PopupData | null>(null);
  const [hasClosedManually, setHasClosedManually] = useState(false);
  const [countdown, setCountdown] = useState<number | null>(null);
  const location = useLocation();

  // Check if we're on an admin page
  const isAdminPage = location.pathname.startsWith('/admin');

  // If we're on an admin page, don't show the popup
  if (isAdminPage) {
    console.log("EventPopup: On admin page, not showing popup");
    return null;
  }

  // Helper function to show popup with delay and countdown
  const showPopupWithDelay = (popupData: PopupData, delay: number = 3000) => {
    console.log("EventPopup: showPopupWithDelay called with popup:", popupData.id, "delay:", delay);
    setPopup(popupData);

    // Show popup after delay to allow main screen to load first
    setTimeout(() => {
      console.log("EventPopup: Showing popup now");
      setIsOpen(true);

      // Start countdown from 10 seconds
      setCountdown(10);

      // Set up countdown timer
      const countdownInterval = setInterval(() => {
        setCountdown(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(countdownInterval);
            console.log("EventPopup: Auto-closing popup after countdown");
            setIsOpen(false);
            return null;
          }
          return prev - 1;
        });
      }, 1000);
    }, delay);
  };

  // Check if this is a fresh visit to the site
  useEffect(() => {
    // TEMPORARY: Clear session storage for testing
    console.log("EventPopup: CLEARING SESSION STORAGE FOR TESTING");
    sessionStorage.removeItem('popupClosedInSession');

    // We'll use sessionStorage to track if popup was closed in this session
    const popupClosedInSession = sessionStorage.getItem('popupClosedInSession');

    if (popupClosedInSession) {
      console.log("EventPopup: Popup was closed in this session, won't show again");
      setHasClosedManually(true);
    } else {
      console.log("EventPopup: New session, popup can be shown");
      setHasClosedManually(false);
    }
  }, []);

  // Fetch active popup from Realtime Database
  useEffect(() => {
    // Don't fetch if user has manually closed the popup
    if (hasClosedManually) {
      console.log("EventPopup: User has manually closed popup, not fetching");
      return;
    }

    const fetchActivePopup = async () => {
      try {
        console.log("EventPopup: Fetching active popups from Realtime Database");
        const now = new Date().getTime();

        const popupsRef = ref(rtdb, 'popups');
        const snapshot = await get(popupsRef);

        if (snapshot.exists()) {
          const data = snapshot.val();
          console.log("EventPopup: Found popup data:", Object.keys(data).length, "entries");

          // Get the most recent active popup
          let activePopup = null;
          let mostRecentDate = 0;

          Object.keys(data).forEach((key) => {
            const popupData = data[key];

            // Convert values to ensure proper comparison
            const isActive = Boolean(popupData.active);
            const startDate = typeof popupData.startDate === 'number' ? popupData.startDate : Number(popupData.startDate);
            const endDate = typeof popupData.endDate === 'number' ? popupData.endDate : Number(popupData.endDate);
            const createdAt = popupData.createdAt || 0;

            const isStarted = startDate <= now;
            const isNotEnded = endDate >= now;

            if (isActive && isStarted && isNotEnded) {
              // If this popup is more recent than our current one, use it instead
              if (createdAt > mostRecentDate) {
                mostRecentDate = createdAt;
                activePopup = {
                  id: key,
                  ...popupData,
                  startDate: new Date(startDate),
                  endDate: new Date(endDate),
                  buttonText: popupData.buttonText || 'Learn More',
                  buttonUrl: popupData.buttonUrl || '/contact/sales'
                };
              }
            }
          });

          if (activePopup) {
            console.log("EventPopup: Found active popup:", activePopup.id);
            showPopupWithDelay(activePopup);
          } else {
            console.log("EventPopup: No active popup found, creating fallback");
            // Create a fallback popup for demonstration
            const fallbackPopup = {
              id: 'fallback',
              title: 'Welcome to Atandra',
              description: 'Discover our latest energy management solutions',
              imageUrl: '/WhatsApp Image 2025-05-22 at 14.45.17_912099e7.jpg',
              buttonText: 'Learn More',
              buttonUrl: '/contact/sales',
              startDate: new Date(),
              endDate: new Date(now + 7 * 24 * 60 * 60 * 1000),
              active: true,
              createdAt: now
            };
            showPopupWithDelay(fallbackPopup);
          }
        } else {
          console.log("EventPopup: No popups found in database, creating fallback");
          // Create a fallback popup for demonstration
          const fallbackPopup = {
            id: 'fallback-no-db',
            title: 'Welcome to Atandra',
            description: 'Discover our latest energy management solutions',
            imageUrl: '/WhatsApp Image 2025-05-22 at 14.45.17_912099e7.jpg',
            buttonText: 'Learn More',
            buttonUrl: '/contact/sales',
            startDate: new Date(),
            endDate: new Date(now + 7 * 24 * 60 * 60 * 1000),
            active: true,
            createdAt: now
          };
          showPopupWithDelay(fallbackPopup);
        }
      } catch (error) {
        console.error('EventPopup: Error fetching popup:', error);
        // Create a fallback popup for demonstration
        const fallbackPopup = {
          id: 'fallback-error',
          title: 'Welcome to Atandra',
          description: 'Discover our latest energy management solutions',
          imageUrl: '/WhatsApp Image 2025-05-22 at 14.45.17_912099e7.jpg',
          buttonText: 'Learn More',
          buttonUrl: '/contact/sales',
          startDate: new Date(),
          endDate: new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000),
          active: true,
          createdAt: new Date().getTime()
        };
        showPopupWithDelay(fallbackPopup);
      }
    };

    fetchActivePopup();
  }, [hasClosedManually]); // Re-run when hasClosedManually changes

  const handleClose = () => {
    setIsOpen(false);
    setCountdown(null); // Clear the countdown
    setHasClosedManually(true); // Prevent popup from showing again in this session
    sessionStorage.setItem('popupClosedInSession', 'true'); // Remember for this session
  };

  if (!popup) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 backdrop-blur-sm p-4"
          onClick={handleClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="relative flex items-center justify-center rounded-xl overflow-hidden shadow-2xl bg-transparent"
            style={{
              width: '90%',
              maxWidth: '600px',
              height: 'auto',
              maxHeight: '70vh',
              aspectRatio: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 z-10 bg-white text-black p-2 rounded-full hover:bg-gray-200 transition-colors shadow-lg"
              aria-label="Close popup"
            >
              <X size={28} strokeWidth={3} />
            </button>

            {/* Countdown timer */}
            {countdown !== null && (
              <div className="absolute top-4 left-4 z-10 bg-white/80 text-black px-3 py-1 rounded-full shadow-lg font-medium">
                Closes in {countdown}s
              </div>
            )}

            {/* Image - Responsive size */}
            <img
              src={popup.imageUrl}
              alt={popup.title || "Event popup"}
              style={{
                display: 'block',
                width: '100%',
                height: 'auto',
                maxHeight: '70vh',
                objectFit: 'contain'
              }}
            />
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EventPopup;
