import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Building, 
  Hospital, 
  Computer, 
  Factory,
  Home,
  Store,
  FileText,
  Mail
} from 'lucide-react';

const ELSeriesOverview = () => {
  // Use cases with detailed descriptions
  const useCases = [
    {
      icon: <Building className="h-8 w-8 text-blue-600" />,
      title: "Small Business Protection",
      description: "Ideal for protecting critical business equipment in small offices, ensuring continuous operations during power outages. Perfect for desktop computers, servers, and networking equipment.",
      applications: ["Desktop Computers", "Small Servers", "Network Switches", "Point-of-Sale Systems"],
      industries: ["Small Offices", "Retail Stores", "Professional Services"]
    },
    {
      icon: <Home className="h-8 w-8 text-blue-600" />,
      title: "Home Office Solutions",
      description: "Perfect for home-based professionals who need reliable power backup for their workstations and networking equipment. Ensures productivity isn't interrupted by power issues.",
      applications: ["Home Workstations", "Internet Routers", "Home Servers", "Security Systems"],
      industries: ["Home Offices", "Remote Workers", "Freelancers"]
    },
    {
      icon: <Store className="h-8 w-8 text-blue-600" />,
      title: "Retail Point-of-Sale",
      description: "Essential for retail environments to prevent transaction data loss and maintain customer service continuity. Protects against revenue loss during power outages.",
      applications: ["POS Terminals", "Cash Registers", "Barcode Scanners", "Receipt Printers"],
      industries: ["Retail Stores", "Restaurants", "Service Centers"]
    },
    {
      icon: <Hospital className="h-8 w-8 text-blue-600" />,
      title: "Medical Equipment",
      description: "Provides reliable power backup for non-critical medical equipment in small clinics and medical offices, ensuring patient care continuity.",
      applications: ["Medical Computers", "Patient Monitoring", "Diagnostic Equipment", "Administrative Systems"],
      industries: ["Small Clinics", "Medical Offices", "Dental Practices"]
    }
  ];

  // Key features with benefits
  const keyFeatures = [
    {
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      title: "Zero Transfer Time",
      description: "True online double conversion technology ensures instantaneous switching with no interruption to connected equipment."
    },
    {
      icon: <Shield className="h-6 w-6 text-blue-600" />,
      title: "Pure Sine Wave Output",
      description: "Delivers clean, stable power that's safe for sensitive electronic equipment and extends equipment lifespan."
    },
    {
      icon: <CheckCircle className="h-6 w-6 text-blue-600" />,
      title: "Wide Input Range",
      description: "Accepts input voltage from 120-280 VAC, providing protection against voltage fluctuations and brownouts."
    },
    {
      icon: <Computer className="h-6 w-6 text-blue-600" />,
      title: "Compact Design",
      description: "Space-efficient tower design fits easily in small offices and home environments without taking up valuable space."
    }
  ];

  // Industries served
  const industries = [
    "Small Offices", "Home Offices", "Retail Stores", "Professional Workstations", 
    "Small Clinics", "Dental Practices", "Service Centers", "Remote Workers"
  ];

  return (
    <PageLayout
      title="EL/ELB Series Overview"
      subtitle="1 kVA to 3 kVA - Essential Power Protection for Small Environments"
      category="protect"
    >
      <div className="font-['Open_Sans']">
        {/* Hero Section - Image Left, Content Right */}
        <div className="relative py-6 md:py-8 overflow-hidden bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
              {/* Product Image - Left Side */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative order-1 lg:order-1"
              >
                <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg">
                  <motion.img
                    src="/UPS/SB_6-removebg-preview.png"
                    alt="EL/ELB Series UPS"
                    className="w-full max-w-xs mx-auto drop-shadow-lg"
                    animate={{
                      y: [0, -5, 0],
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 4,
                      ease: "easeInOut"
                    }}
                  />
                </div>
              </motion.div>

              {/* Content - Right Side */}
              <motion.div
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="space-y-4 order-2 lg:order-2 font-['Open_Sans']"
              >
                <div className="inline-block bg-blue-600 text-white px-3 py-1.5 rounded-full text-sm font-semibold font-['Open_Sans']">
                  KRYKARD EL/ELB Series
                </div>

                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white leading-tight font-['Open_Sans']">
                  Essential Power Protection for <span className="text-blue-600">Small Environments</span>
                </h1>

                <p className="text-base md:text-lg text-gray-600 dark:text-gray-400 leading-relaxed font-['Open_Sans']">
                  The EL/ELB Series provides essential power protection for small office environments, home offices, and individual workstations. Designed with true online double conversion technology, it ensures zero transfer time during power outages while delivering clean, stable power to sensitive electronic equipment.
                </p>

                <div className="flex flex-wrap gap-3">
                  <Link to="/contact/sales">
                    <motion.button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2.5 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans'] font-semibold"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Mail className="h-4 w-4" />
                      Request Quote
                    </motion.button>
                  </Link>

                  <Link to="/protect/ups/el-series">
                    <motion.button
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-5 py-2.5 rounded-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans'] font-semibold"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FileText className="h-4 w-4" />
                      View Technical Details
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Key Features Section */}
        <div className="py-6 md:py-8 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Key Features & Benefits
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Advanced technology designed for reliability and performance
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {keyFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 p-5 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2.5 rounded-lg">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 font-['Open_Sans']">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed font-['Open_Sans']">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div className="py-6 md:py-8 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Real-World Applications
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Discover how the EL/ELB Series protects critical operations across various industries
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {useCases.map((useCase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2.5 rounded-lg">
                      {useCase.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white font-['Open_Sans']">
                      {useCase.title}
                    </h3>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4 font-['Open_Sans']">
                    {useCase.description}
                  </p>

                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-['Open_Sans']">
                        Typical Applications:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {useCase.applications.map((app, i) => (
                          <span
                            key={i}
                            className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2.5 py-1 rounded-full text-sm font-medium font-['Open_Sans']"
                          >
                            {app}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-['Open_Sans']">
                        Industries:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {useCase.industries.map((industry, i) => (
                          <span
                            key={i}
                            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2.5 py-1 rounded-full text-sm font-medium font-['Open_Sans']"
                          >
                            {industry}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Industries Served Section */}
        <div className="py-6 md:py-8 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Industries We Serve
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Trusted by businesses across multiple sectors for reliable power protection
              </p>
            </motion.div>

            <div className="flex flex-wrap justify-center gap-3">
              {industries.map((industry, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: index * 0.05 }}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 font-['Open_Sans']"
                >
                  {industry}
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="py-8 md:py-10 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Ready to Protect Your Equipment?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto font-['Open_Sans']">
                Get expert consultation and find the perfect EL/ELB Series UPS for your specific needs
              </p>

              <div className="flex flex-wrap justify-center gap-3">
                <Link to="/contact/sales">
                  <motion.button
                    className="bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 rounded-lg font-bold shadow-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans']"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Mail className="h-4 w-4" />
                    Get Quote Now
                  </motion.button>
                </Link>

                <Link to="/protect/ups/el-series">
                  <motion.button
                    className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-3 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 font-['Open_Sans']"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FileText className="h-4 w-4" />
                    View Technical Specs
                    <ArrowRight className="h-4 w-4" />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default ELSeriesOverview;
