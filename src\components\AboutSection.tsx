import React from 'react';
import { motion } from 'framer-motion';
import { Trophy, Users, MapPin, Clock, Star, Target, Award, Shield } from 'lucide-react';

const AboutSection = () => {
  const achievements = [
    {
      icon: Trophy,
      title: "India's #1",
      subtitle: "Servo Stabilizer Manufacturer",
      color: "from-yellow-400 to-orange-500",
      bgColor: "from-yellow-50 to-orange-50"
    },
    {
      icon: MapPin,
      title: "100+",
      subtitle: "Service Centers Nationwide",
      color: "from-blue-400 to-blue-600",
      bgColor: "from-blue-50 to-blue-100"
    },
    {
      icon: Users,
      title: "10,000+",
      subtitle: "Satisfied Customers",
      color: "from-green-400 to-green-600",
      bgColor: "from-green-50 to-green-100"
    },
    {
      icon: Clock,
      title: "40+ Years",
      subtitle: "Industry Experience",
      color: "from-purple-400 to-purple-600",
      bgColor: "from-purple-50 to-purple-100"
    },
    {
      icon: Shield,
      title: "CE Certified",
      subtitle: "Quality Products",
      color: "from-red-400 to-red-600",
      bgColor: "from-red-50 to-red-100"
    },
    {
      icon: Award,
      title: "ISO Certified",
      subtitle: "Quality Standards",
      color: "from-indigo-400 to-indigo-600",
      bgColor: "from-indigo-50 to-indigo-100"
    }
  ];

  const features = [
    {
      icon: Target,
      title: "Mission Critical Solutions",
      description: "Delivering power solutions that keep your operations running 24/7 without interruption."
    },
    {
      icon: Shield,
      title: "Uncompromising Quality",
      description: "Every product undergoes rigorous testing to ensure reliability and performance excellence."
    },
    {
      icon: Users,
      title: "Expert Support Team",
      description: "Dedicated technical experts providing round-the-clock support and maintenance services."
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-6 font-['Open_Sans']">
            <Star className="h-4 w-4" />
            <span>Our Legacy</span>
          </div>
          
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-6 font-['Open_Sans']">
            Four Decades of
            <span className="block text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text">
              Power Excellence
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 max-w-4xl mx-auto font-['Open_Sans'] leading-relaxed">
            Since our inception, Atandra Energy has been at the forefront of power and energy management solutions, 
            serving industries across India with unwavering commitment to quality and innovation.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <div className="space-y-8">
              <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-200 shadow-xl">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 font-['Open_Sans']">
                  Powering India's Growth
                </h3>
                <p className="text-gray-600 leading-relaxed font-['Open_Sans']">
                  Headquartered in Chennai, Atandra Energy draws upon a rich foundation of more than 40 years 
                  of expertise in Power & Energy Management. Our state-of-the-art facilities empower us to 
                  address the comprehensive requirements of Indian industries effectively and efficiently.
                </p>
              </div>

              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-8 border border-blue-200">
                <h3 className="text-2xl font-bold text-blue-900 mb-4 font-['Open_Sans']">
                  Sustainability Commitment
                </h3>
                <p className="text-blue-800 leading-relaxed font-['Open_Sans']">
                  We ensure industries derive maximum benefits from our power conditioning and energy 
                  management solutions, contributing to a more sustainable and efficient industrial ecosystem 
                  across the nation.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Right Content - Features */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100"
                whileHover={{ y: -5, scale: 1.02 }}
              >
                <div className="flex items-start space-x-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-lg font-bold text-gray-900 mb-2 font-['Open_Sans'] group-hover:text-blue-600 transition-colors">
                      {feature.title}
                    </h4>
                    <p className="text-gray-600 font-['Open_Sans'] leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Achievements Grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4 font-['Open_Sans']">
              Our Achievements
            </h3>
            <p className="text-lg text-gray-600 font-['Open_Sans']">
              Numbers that speak to our commitment and success
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                className={`relative bg-gradient-to-br ${achievement.bgColor} rounded-2xl p-6 text-center border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden`}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.05 }}
              >
                {/* Background Glow */}
                <div className={`absolute inset-0 bg-gradient-to-br ${achievement.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
                
                <div className="relative z-10">
                  <div className={`inline-flex p-3 rounded-2xl bg-gradient-to-br ${achievement.color} text-white mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                    <achievement.icon className="h-6 w-6" />
                  </div>
                  <div className={`text-2xl font-bold bg-gradient-to-br ${achievement.color} bg-clip-text text-transparent mb-2 font-['Open_Sans']`}>
                    {achievement.title}
                  </div>
                  <div className="text-sm text-gray-600 font-medium font-['Open_Sans'] leading-tight">
                    {achievement.subtitle}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;