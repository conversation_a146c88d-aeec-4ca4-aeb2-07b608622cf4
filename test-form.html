<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .checkbox-item input[type="checkbox"] {
            width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Form Submission</h1>
        <p>This is a test page to verify the backend API is working correctly.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="name">Name*</label>
                <input type="text" id="name" name="name" required value="Test User">
            </div>
            
            <div class="form-group">
                <label for="email">Email*</label>
                <input type="email" id="email" name="email" required value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="company">Company*</label>
                <input type="text" id="company" name="company" required value="Test Company">
            </div>
            
            <div class="form-group">
                <label for="designation">Designation*</label>
                <input type="text" id="designation" name="designation" required value="Manager">
            </div>
            
            <div class="form-group">
                <label for="city">City*</label>
                <input type="text" id="city" name="city" required value="Chennai">
            </div>
            
            <div class="form-group">
                <label for="mobile">Mobile*</label>
                <input type="text" id="mobile" name="mobile" required value="+91 9876543210">
            </div>
            
            <div class="form-group">
                <label for="pincode">Pincode*</label>
                <input type="text" id="pincode" name="pincode" required value="600001">
            </div>
            
            <div class="form-group">
                <label for="products">Products*</label>
                <select id="products" name="products" required>
                    <option value="">Select Product Category</option>
                    <option value="measure" selected>Measure</option>
                    <option value="protect">Protect</option>
                    <option value="conserve">Conserve</option>
                    <option value="consultation">Consultation</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="remarks">Remarks</label>
                <textarea id="remarks" name="remarks" rows="3" placeholder="Additional information...">Test form submission from HTML test page</textarea>
            </div>
            
            <div class="form-group">
                <label>Service Requests:</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="requestDemo" name="requestDemo" checked>
                        <label for="requestDemo">Request Demo</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="requestCallback" name="requestCallback">
                        <label for="requestCallback">Request Call Back</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="sendDetails" name="sendDetails" checked>
                        <label for="sendDetails">Send Product Details</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="sendUpdates" name="sendUpdates">
                        <label for="sendUpdates">Send Product Updates</label>
                    </div>
                </div>
            </div>
            
            <button type="submit" id="submitBtn">Submit Test Form</button>
        </form>
        
        <div id="message"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';
            
            // Clear previous messages
            messageDiv.innerHTML = '';
            
            try {
                // Collect form data
                const formData = {
                    name: document.getElementById('name').value,
                    email: document.getElementById('email').value,
                    company: document.getElementById('company').value,
                    designation: document.getElementById('designation').value,
                    city: document.getElementById('city').value,
                    mobile: document.getElementById('mobile').value,
                    pincode: document.getElementById('pincode').value,
                    products: document.getElementById('products').value,
                    remarks: document.getElementById('remarks').value,
                    requestDemo: document.getElementById('requestDemo').checked,
                    requestCallback: document.getElementById('requestCallback').checked,
                    sendDetails: document.getElementById('sendDetails').checked,
                    sendUpdates: document.getElementById('sendUpdates').checked
                };
                
                console.log('Submitting form data:', formData);
                
                // Submit to backend API
                const response = await fetch('http://localhost:5007/api/email/send-enquiry', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });
                
                const result = await response.json();
                console.log('API Response:', result);
                
                if (result.success) {
                    messageDiv.innerHTML = `<div class="message success">
                        <strong>Success!</strong><br>
                        ${result.message}<br>
                        <small>Message ID: ${result.data.messageId}</small><br>
                        <small>Email Sent: ${result.data.emailSent ? 'Yes' : 'No (logged for manual processing)'}</small>
                    </div>`;
                } else {
                    messageDiv.innerHTML = `<div class="message error">
                        <strong>Error!</strong><br>
                        ${result.message || 'Failed to submit form'}
                    </div>`;
                }
                
            } catch (error) {
                console.error('Form submission error:', error);
                messageDiv.innerHTML = `<div class="message error">
                    <strong>Network Error!</strong><br>
                    ${error.message}<br>
                    Please check if the backend server is running on port 5007.
                </div>`;
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Submit Test Form';
            }
        });
    </script>
</body>
</html>
