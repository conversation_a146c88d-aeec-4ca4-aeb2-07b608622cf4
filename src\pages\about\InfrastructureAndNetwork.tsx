import React, { useState, useEffect } from 'react';
import { 
  MapPin, Zap, HardDrive, Network, Cloud, Wifi, Battery, TrendingUp,
  Monitor, Settings, BarChart3, Building, Wrench, Target, Shield, 
  Factory, Cpu, Globe, Users, Award, Server
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';

const KrykardTimeline = () => {
  const [activeEvent, setActiveEvent] = useState(null);

  // All events in chronological order
  const allEventsData = [
    {
      id: 0,
      year: "1985",
      icon: Zap,
      iconColor: "text-yellow-600",
      bgColor: "bg-yellow-50",
      title: "Launched KRYKARD brand",
      description: "",
      position: "top"
    },
    {
      id: 1,
      year: "1986",
      icon: Monitor,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-50",
      title: "Launched Servo Stabiliser with Digital Display",
      description: "first in India",
      position: "bottom"
    },
    {
      id: 2,
      year: "1989",
      icon: HardDrive,
      iconColor: "text-indigo-600",
      bgColor: "bg-indigo-50",
      title: "Launched Portable Load Managers",
      description: "first in India",
      position: "top"
    },
    {
      id: 3,
      year: "1993",
      icon: Settings,
      iconColor: "text-green-600",
      bgColor: "bg-green-50",
      title: "Launched Panel Load Managers",
      description: "first in India",
      position: "bottom"
    },
    {
      id: 4,
      year: "1995",
      icon: Network,
      iconColor: "text-teal-600",
      bgColor: "bg-teal-50",
      title: "Installed first EMS Network",
      description: "",
      position: "top"
    },
    {
      id: 5,
      year: "2003",
      icon: BarChart3,
      iconColor: "text-purple-600",
      bgColor: "bg-purple-50",
      title: "Launched PQ Analyzers",
      description: "first in India",
      position: "bottom"
    },
    {
      id: 6,
      year: "2017",
      icon: Cloud,
      iconColor: "text-cyan-600",
      bgColor: "bg-cyan-50",
      title: "Launched Cloud Service portal & EFSR",
      description: "",
      position: "top"
    },
    {
      id: 7,
      year: "2018",
      icon: Building,
      iconColor: "text-orange-600",
      bgColor: "bg-orange-50",
      title: "Moved to new factory at Keezhkattalai",
      description: "",
      position: "bottom"
    },
    {
      id: 8,
      year: "2019",
      icon: Wifi,
      iconColor: "text-emerald-600",
      bgColor: "bg-emerald-50",
      title: "Launched Online UPS",
      description: "Installed 3.5MVA Servo Stabilizer",
      position: "top"
    },
    {
      id: 9,
      year: "2021",
      icon: Wrench,
      iconColor: "text-red-600",
      bgColor: "bg-red-50",
      title: "Launched Industry IOT 4.0 solutions",
      description: "",
      position: "bottom"
    },
    {
      id: 10,
      year: "2022",
      icon: Battery,
      iconColor: "text-violet-600",
      bgColor: "bg-violet-50",
      title: "Launched Static Voltage Regulators",
      description: "",
      position: "top"
    },
    {
      id: 12,
      year: "2024",
      icon: TrendingUp,
      iconColor: "text-rose-600",
      bgColor: "bg-rose-50",
      title: "Started the 100th Service Centre in India",
      description: "",
      position: "bottom"
    },
    {
      id: 11,
      year: "Now & Beyond",
      icon: Target,
      iconColor: "text-pink-600",
      bgColor: "bg-pink-50",
      title: "Our Journey of Growth continues...",
      description: "",
      position: "top"
    }
  ];

  const topEvents = allEventsData.filter(event => event.position === "top");
  const bottomEvents = allEventsData.filter(event => event.position === "bottom");
  const allEvents = allEventsData;

  const EventCard = ({ event, isTop = false, index }) => (
    <div 
      className={`flex flex-col items-center cursor-pointer transition-all duration-300 ${
        activeEvent === event.id ? 'scale-105' : 'hover:scale-102'
      } ${isTop ? 'mb-6 sm:mb-8 md:mb-12' : 'mt-6 sm:mt-8 md:mt-12'}`}
      onClick={() => setActiveEvent(activeEvent === event.id ? null : event.id)}
      style={{ 
        minWidth: '100px', 
        maxWidth: '120px'
      }}
    >
      <div className={`flex flex-col items-center ${isTop ? 'order-2' : ''}`}>
        <div className={`text-sm sm:text-base font-bold mb-2 sm:mb-3 ${isTop ? 'order-3' : 'order-1'} text-black text-center`}>
          {event.year}
        </div>
        
        <div className={`
          w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg
          ${isTop ? 'order-2' : 'order-2'}
          ${event.bgColor} border-2 border-white
        `}>
          <event.icon size={16} className={`sm:w-5 sm:h-5 md:w-6 md:h-6 ${event.iconColor}`} />
        </div>
        
        <div className={`mt-2 sm:mt-3 md:mt-4 text-center px-1 ${isTop ? 'order-1' : 'order-3'}`} style={{ maxWidth: '110px' }}>
          <h3 className="font-bold text-sm sm:text-base md:text-base leading-tight mb-1 text-black">
            <strong>{event.title}</strong>
          </h3>
          {event.description && (
            <p className="text-xs sm:text-sm leading-tight text-black hidden sm:block">
              {event.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-full bg-white p-2 sm:p-4">
      <div className="text-center mb-4 sm:mb-6 md:mb-8">
        <h1 className="text-lg sm:text-2xl md:text-3xl font-bold text-black mb-2">
          <strong>Krykard's Journey of Innovation and Growth</strong>
        </h1>
        <p className="text-sm md:text-base text-gray-600 max-w-3xl mx-auto leading-relaxed">
          From humble beginnings to industry leadership - Four decades of relentless innovation, unwavering commitment, and transformative excellence
        </p>
      </div>

      <div className="max-w-full mx-auto overflow-x-auto">
        <div className="min-w-[1200px] px-4">
          <div className="flex justify-between items-end">
            {topEvents.map((event, index) => (
              <EventCard key={event.id} event={event} isTop={true} index={index} />
            ))}
          </div>

          <div className="relative">
            <div className="h-0.5 sm:h-1 bg-black relative">
              <div className="absolute inset-0 flex justify-between items-center">
                {allEvents.map((event, index) => (
                  <div 
                    key={event.id}
                    className="w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-black transition-all duration-300 hover:bg-gray-700 cursor-pointer"
                    style={{ 
                      position: 'relative',
                      marginTop: '-6px'
                    }}
                    onClick={() => setActiveEvent(activeEvent === event.id ? null : event.id)}
                  />
                ))}
              </div>
              
              <div className="absolute -right-2 sm:-right-3 top-1/2 transform -translate-y-1/2">
                <div className="w-0 h-0 border-l-4 sm:border-l-6 border-l-black border-t-2 sm:border-t-3 border-t-transparent border-b-2 sm:border-b-3 border-b-transparent"></div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-start">
            {bottomEvents.map((event, index) => (
              <EventCard key={event.id} event={event} isTop={false} index={index} />
            ))}
          </div>
        </div>
      </div>

      {activeEvent !== null && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t-2 border-blue-200 shadow-2xl p-3 sm:p-4 md:p-6 transform transition-transform duration-300 z-50 max-h-48 overflow-y-auto">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-2 sm:space-x-4 flex-1">
              <div className={`w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center ${
                allEvents.find(e => e.id === activeEvent)?.bgColor || 'bg-blue-50'
              }`}>
                {React.createElement(
                  allEvents.find(e => e.id === activeEvent)?.icon || Zap,
                  { 
                    size: 16, 
                    className: `sm:w-5 sm:h-5 md:w-6 md:h-6 ${allEvents.find(e => e.id === activeEvent)?.iconColor || 'text-blue-600'}`
                  }
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-sm sm:text-base md:text-lg font-bold text-black truncate">
                  <strong>{allEvents.find(e => e.id === activeEvent)?.year} - {allEvents.find(e => e.id === activeEvent)?.title}</strong>
                </h3>
                <p className="text-xs sm:text-sm text-black">
                  {allEvents.find(e => e.id === activeEvent)?.description}
                </p>
              </div>
            </div>
            <button 
              onClick={() => setActiveEvent(null)}
              className="text-black hover:text-gray-700 text-xl sm:text-2xl font-bold ml-2 flex-shrink-0"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// New Powerful Content Section Component
const PowerfulJourneyContent = () => {
  return (
    <section className="py-8 md:py-12 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <div className="inline-block">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-6 text-black" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              Four Decades of Relentless Excellence
            </h2>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <p className="text-base md:text-lg lg:text-xl text-black leading-relaxed font-medium text-center md:text-justify" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              From a visionary startup in 1985 to becoming India's most trusted electrical solutions provider, 
              KRYKARD's journey is a testament to unwavering determination, groundbreaking innovation, and 
              relentless pursuit of excellence. Every milestone achieved through countless hours of dedication, 
              every breakthrough born from passionate engineering, and every success story is written with the 
              sweat and sacrifice of our incredible team. Today, with <span className="font-bold text-yellow-500">100+ service centers</span> across the India and 
              <span className="font-bold text-blue-600"> 5 lakh+ satisfied customers</span>, we stand as a beacon of 
              Indian innovation and entrepreneurial spirit - proving that with hard work, vision, and unwavering commitment, 
              no dream is too big to achieve.
            </p>
          </div>

          {/* Powerful Keywords Section */}
          <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white p-3 rounded-lg shadow-md border-l-4 border-yellow-500">
              <div className="text-lg font-bold text-yellow-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>INNOVATION</div>
              <div className="text-xs text-gray-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>Breakthrough Solutions</div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-md border-l-4 border-blue-500">
              <div className="text-lg font-bold text-blue-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>EXCELLENCE</div>
              <div className="text-xs text-gray-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>Uncompromising Quality</div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-md border-l-4 border-green-500">
              <div className="text-lg font-bold text-green-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>DEDICATION</div>
              <div className="text-xs text-gray-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>Relentless Commitment</div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-md border-l-4 border-yellow-500">
              <div className="text-lg font-bold text-yellow-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>GROWTH</div>
              <div className="text-xs text-gray-600" style={{ fontFamily: 'Open Sans, sans-serif' }}>Unstoppable Progress</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

const InfrastructureAndNetwork = () => {
  const [visibleItems, setVisibleItems] = useState(new Set());

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setVisibleItems(prev => new Set([
              ...prev,
              parseInt((entry.target as HTMLElement).dataset.index || "0")
            ]));
          }
        });
      },
      { threshold: 0.1 }
    );

    const items = document.querySelectorAll('.timeline-item');
    items.forEach((item) => observer.observe(item));

    return () => observer.disconnect();
  }, []);

  return (
    <PageLayout title="Infrastructure & Network" category="about">
      <div className="min-h-screen bg-white" style={{ fontFamily: 'Open Sans, sans-serif' }}>
      
      {/* Compact Hero Section */}
      <section className="py-6 md:py-8 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3">
            <span className="bg-gradient-to-r from-blue-600 via-green-600 to-yellow-500 bg-clip-text text-transparent">
              Our Innovation Journey
            </span>
          </h1>
          <p className="text-base md:text-lg text-gray-600 leading-relaxed">
            Four decades of pioneering excellence in electrical solutions
          </p>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-2 sm:py-4 md:py-6">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
          <KrykardTimeline />
        </div>
      </section>

      {/* New Powerful Journey Content Section */}
      <PowerfulJourneyContent />

      {/* Compact Worldwide Presence Section */}
      <section className="py-8 md:py-12 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 items-center">

            {/* Left Content */}
            <div className="text-center lg:text-left">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-black mb-4">
                WORLDWIDE <span className="bg-gradient-to-r from-yellow-500 via-green-500 to-blue-500 bg-clip-text text-transparent">PRESENCE</span>
              </h2>
              <p className="text-sm md:text-base text-black mb-3 leading-relaxed">
                With over <span className="font-bold text-green-600">100+ service centers</span> across India and a growing international footprint, KRYKARD serves customers globally with innovative power management solutions.
              </p>
              <p className="text-sm md:text-base text-black mb-4 leading-relaxed">
                Our commitment to excellence has made us the <span className="font-bold text-blue-600">preferred choice</span> for large corporates, OEMs, and industrial establishments worldwide.
              </p>
            </div>

            {/* Right Content - Compact Stats */}
            <div className="grid grid-cols-2 gap-3 md:gap-4">
              <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-2 border-yellow-200 p-3 md:p-4 text-center rounded-xl transition-all duration-300">
                <div className="text-yellow-500 mb-1 md:mb-2 flex justify-center">
                  <Building className="w-6 h-6 md:w-8 md:h-8" />
                </div>
                <div className="text-xl md:text-2xl font-bold text-yellow-600 mb-1">100+</div>
                <div className="text-xs md:text-sm text-black font-medium">Service Centers</div>
              </div>
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border-2 border-blue-200 p-3 md:p-4 text-center rounded-xl transition-all duration-300">
                <div className="text-blue-500 mb-1 md:mb-2 flex justify-center">
                  <Award className="w-6 h-6 md:w-8 md:h-8" />
                </div>
                <div className="text-xl md:text-2xl font-bold text-blue-600 mb-1">40+</div>
                <div className="text-xs md:text-sm text-black font-medium">Years Experience</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 border-2 border-green-200 p-3 md:p-4 text-center rounded-xl transition-all duration-300">
                <div className="text-green-500 mb-1 md:mb-2 flex justify-center">
                  <Globe className="w-6 h-6 md:w-8 md:h-8" />
                </div>
                <div className="text-xl md:text-2xl font-bold text-green-600 mb-1">5L+</div>
                <div className="text-xs md:text-sm text-black font-medium">Installations</div>
              </div>
              <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-2 border-yellow-200 p-3 md:p-4 text-center rounded-xl transition-all duration-300">
                <div className="text-yellow-500 mb-1 md:mb-2 flex justify-center">
                  <Users className="w-6 h-6 md:w-8 md:h-8" />
                </div>
                <div className="text-xl md:text-2xl font-bold text-yellow-600 mb-1">500+</div>
                <div className="text-xs md:text-sm text-black font-medium">Employees</div>
              </div>
            </div>
          </div>

          {/* Compact Image Section */}
          <div className="mt-6 md:mt-8">
            <img
              src="/background_images/krykard.jpg"
              alt="KRYKARD Worldwide Presence"
              className="w-full h-auto rounded-xl"
            />
          </div>
        </div>
      </section>

      {/* Simple Clean Contact Section - No Background */}
      <section className="py-8 md:py-12 bg-white">
        <div className="px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-3">
            Ready to Experience Excellence?
          </h2>
          <p className="text-sm md:text-base text-gray-600 mb-4 max-w-2xl mx-auto">
            Join thousands of satisfied customers who trust KRYKARD for their electrical solutions.
          </p>
          <button className="bg-gradient-to-r from-blue-600 via-green-600 to-yellow-500 text-white px-6 py-2 md:px-8 md:py-3 rounded-full font-semibold hover:opacity-90 transition-opacity duration-300">
            Contact Us Today
          </button>
        </div>
      </section>

      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');
        
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in-up {
          animation: fade-in-up 0.5s ease-out forwards;
        }
        
        .group:hover .invisible {
          visibility: visible;
        }
        
        .timeline-item:focus {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
        }
        
        html {
          scroll-behavior: smooth;
        }
        
        /* Mobile Timeline Optimizations */
        @media (max-width: 640px) {
          .timeline-container {
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
          }
          
          .timeline-container::-webkit-scrollbar {
            display: none;
          }
          
          .event-card {
            flex-shrink: 0;
            min-width: 80px;
          }
          
          .event-title {
            font-size: 10px;
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
        
        /* Tablet Optimizations */
        @media (min-width: 641px) and (max-width: 1024px) {
          .event-card {
            min-width: 100px;
            max-width: 120px;
          }
        }
        
        /* Desktop Optimizations */
        @media (min-width: 1025px) {
          .event-card {
            min-width: 120px;
            max-width: 140px;
          }
        }
      `}</style>
    </div>
    </PageLayout>
  );
};

export default InfrastructureAndNetwork;