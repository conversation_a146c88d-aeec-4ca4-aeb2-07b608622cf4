import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Phone, Clock, Wrench, Shield, CheckCircle, ArrowRight, Headphones } from 'lucide-react';

const ServicesSection = () => {
  const serviceFeatures = [
    {
      icon: MapPin,
      title: "Nationwide Coverage",
      description: "100+ service centers across every state and key industrial region",
      color: "from-red-500 to-pink-600"
    },
    {
      icon: Clock,
      title: "24-Hour Response",
      description: "Guaranteed on-site support within 24 hours, 365 days a year",
      color: "from-yellow-500 to-orange-600"
    },
    {
      icon: Wrench,
      title: "Certified Technicians",
      description: "Local engineers trained on latest power management technologies",
      color: "from-blue-500 to-cyan-600"
    },
    {
      icon: Shield,
      title: "Genuine Parts",
      description: "Authentic replacement parts available at every service center",
      color: "from-gray-500 to-gray-700"
    },
    {
      icon: Headphones,
      title: "24/7 Helpline",
      description: "Toll-free support line for instant troubleshooting assistance",
      color: "from-green-500 to-emerald-600"
    },
    {
      icon: CheckCircle,
      title: "Preventive Maintenance",
      description: "Scheduled inspections and maintenance to minimize downtime",
      color: "from-purple-500 to-violet-600"
    }
  ];

  const serviceStats = [
    { value: "100+", label: "Service Centers", color: "from-blue-500 to-blue-600" },
    { value: "24/7", label: "Support Available", color: "from-green-500 to-green-600" },
    { value: "24hrs", label: "Response Time", color: "from-purple-500 to-purple-600" },
    { value: "100%", label: "Genuine Parts", color: "from-orange-500 to-orange-600" }
  ];

  return (
    <section id="services" className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6 font-['Open_Sans']">
            <MapPin className="h-4 w-4" />
            <span>Service Network</span>
          </div>
          
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black text-white mb-6 font-['Open_Sans']">
            Nationwide Service
            <span className="block text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
              Excellence
            </span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-4xl mx-auto font-['Open_Sans'] leading-relaxed">
            With over 100 service centers across India, we ensure that expert support is always within reach. 
            Our commitment to service excellence keeps your operations running smoothly, 24/7.
          </p>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center mb-16">
          {/* Left Features */}
          <motion.div
            className="lg:col-span-4 space-y-6"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            {serviceFeatures.slice(0, 3).map((feature, index) => (
              <motion.div
                key={index}
                className="group bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300"
                whileHover={{ x: 10, scale: 1.02 }}
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-2xl bg-gradient-to-br ${feature.color} shadow-lg`}>
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-white mb-2 font-['Open_Sans']">
                      {feature.title}
                    </h4>
                    <p className="text-gray-300 font-['Open_Sans'] text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Center - India Map */}
          <motion.div
            className="lg:col-span-4 flex justify-center"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="relative">
              <div className="w-80 h-80 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20 shadow-2xl">
                <div className="w-64 h-64 bg-gradient-to-br from-white/10 to-white/5 rounded-full flex items-center justify-center border border-white/20">
                  <div className="text-center">
                    <MapPin className="h-20 w-20 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-2xl font-bold text-white mb-2 font-['Open_Sans']">India Coverage</h3>
                    <p className="text-blue-300 font-['Open_Sans']">100+ Service Centers</p>
                  </div>
                </div>
              </div>
              
              {/* Floating elements */}
              <motion.div
                className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-xl"
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <span className="text-white font-bold font-['Open_Sans']">24/7</span>
              </motion.div>
              
              <motion.div
                className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-xl"
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 3, repeat: Infinity, delay: 1.5 }}
              >
                <CheckCircle className="h-8 w-8 text-white" />
              </motion.div>
            </div>
          </motion.div>

          {/* Right Features */}
          <motion.div
            className="lg:col-span-4 space-y-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {serviceFeatures.slice(3, 6).map((feature, index) => (
              <motion.div
                key={index}
                className="group bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300"
                whileHover={{ x: -10, scale: 1.02 }}
              >
                <div className="flex items-start space-x-4 text-right">
                  <div>
                    <h4 className="text-lg font-bold text-white mb-2 font-['Open_Sans']">
                      {feature.title}
                    </h4>
                    <p className="text-gray-300 font-['Open_Sans'] text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                  <div className={`p-3 rounded-2xl bg-gradient-to-br ${feature.color} shadow-lg`}>
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {serviceStats.map((stat, index) => (
            <motion.div
              key={index}
              className={`bg-gradient-to-br ${stat.color} rounded-2xl p-8 text-center shadow-2xl hover:shadow-3xl transition-all duration-300`}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -5, scale: 1.05 }}
            >
              <div className="text-4xl font-black text-white mb-2 font-['Open_Sans']">
                {stat.value}
              </div>
              <div className="text-white/80 font-semibold font-['Open_Sans']">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm rounded-3xl p-12 border border-white/20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h3 className="text-3xl font-bold text-white mb-4 font-['Open_Sans']">
            Need Immediate Support?
          </h3>
          <p className="text-gray-300 mb-8 font-['Open_Sans'] text-lg">
            Our expert team is standing by to help you with any power management needs
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 shadow-xl hover:shadow-2xl flex items-center justify-center space-x-2 font-['Open_Sans']"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Phone className="h-5 w-5" />
              <span>Call Now</span>
            </motion.button>
            
            <motion.button
              className="bg-white/10 backdrop-blur-sm border border-white/30 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:bg-white/20 flex items-center justify-center space-x-2 font-['Open_Sans']"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Request Service</span>
              <ArrowRight className="h-5 w-5" />
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;