"""
Email Service for sending enquiry emails and confirmations
Replaces the Node.js nodemailer functionality with Python smtplib
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import formataddr
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class EmailService:
    """Email service for handling SMTP operations"""
    
    def __init__(self):
        """Initialize email service with SMTP configuration"""
        self.smtp_host = os.environ.get('SMTP_HOST', 'smtp.mail.yahoo.com')
        self.smtp_port = int(os.environ.get('SMTP_PORT', 587))
        self.smtp_secure = os.environ.get('SMTP_SECURE', 'false').lower() == 'true'
        self.smtp_user = os.environ.get('SMTP_USER')
        self.smtp_pass = os.environ.get('SMTP_PASS')
        self.from_email = os.environ.get('FROM_EMAIL', self.smtp_user)
        self.from_name = os.environ.get('FROM_NAME', 'Atandra Energy Sales Team')
        self.to_email = os.environ.get('TO_EMAIL', self.smtp_user)
        
        logger.info("✅ Email service initialized successfully")
    
    def verify_connection(self) -> bool:
        """Verify SMTP connection"""
        try:
            # Create SSL context
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            # Connect to server
            if self.smtp_secure:
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port, context=context)
            else:
                server = smtplib.SMTP(self.smtp_host, self.smtp_port)
                server.starttls(context=context)
            
            # Login
            server.login(self.smtp_user, self.smtp_pass)
            server.quit()
            
            logger.info("✅ SMTP connection verified")
            return True
            
        except Exception as error:
            logger.error(f"❌ SMTP connection failed: {str(error)}")
            return False
    
    def generate_email_html(self, form_data: Dict[str, Any]) -> str:
        """Generate HTML email template for sales enquiry"""
        
        # Extract form data
        name = form_data.get('name', '')
        email = form_data.get('email', '')
        company = form_data.get('company', '')
        designation = form_data.get('designation', '')
        city = form_data.get('city', '')
        mobile = form_data.get('mobile', '')
        pincode = form_data.get('pincode', '')
        products = form_data.get('products', '')
        remarks = form_data.get('remarks', '')
        request_demo = form_data.get('requestDemo', False)
        request_callback = form_data.get('requestCallback', False)
        send_details = form_data.get('sendDetails', False)
        send_updates = form_data.get('sendUpdates', False)
        
        # Generate service requests section
        service_requests = []
        if request_demo:
            service_requests.append('<div>✅ Request Demo</div>')
        if request_callback:
            service_requests.append('<div>✅ Request Call Back</div>')
        if send_details:
            service_requests.append('<div>✅ Send Product Details</div>')
        if send_updates:
            service_requests.append('<div>✅ Send Product Updates</div>')
        
        service_requests_html = ''.join(service_requests) if service_requests else '<div>No specific service requests</div>'
        
        # Generate remarks section
        remarks_section = f'''
        <h2>Additional Information</h2>
        <div class="field"><span class="label">Remarks:</span><span class="value">{remarks}</span></div>
        ''' if remarks else ''
        
        html_template = f'''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Sales Enquiry</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #000000, #333333); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .field {{ margin-bottom: 15px; }}
                .label {{ font-weight: bold; color: #555; }}
                .value {{ margin-left: 10px; }}
                .checkbox-section {{ background: #e8f4f8; padding: 15px; border-radius: 5px; margin-top: 20px; }}
                .footer {{ text-align: center; margin-top: 20px; color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔔 New Sales Enquiry</h1>
                    <p>Atandra Energy Private Limited</p>
                </div>
                <div class="content">
                    <h2>Contact Information</h2>
                    <div class="field"><span class="label">Name:</span><span class="value">{name}</span></div>
                    <div class="field"><span class="label">Email:</span><span class="value">{email}</span></div>
                    <div class="field"><span class="label">Company:</span><span class="value">{company}</span></div>
                    <div class="field"><span class="label">Designation:</span><span class="value">{designation}</span></div>
                    <div class="field"><span class="label">City:</span><span class="value">{city}</span></div>
                    <div class="field"><span class="label">Mobile:</span><span class="value">{mobile}</span></div>
                    <div class="field"><span class="label">Pincode:</span><span class="value">{pincode}</span></div>
                    
                    <h2>Product Interest</h2>
                    <div class="field"><span class="label">Products Interested:</span><span class="value">{products}</span></div>
                    
                    {remarks_section}
                    
                    <div class="checkbox-section">
                        <h3>Service Requests</h3>
                        {service_requests_html}
                    </div>
                    
                    <div class="footer">
                        <p>This enquiry was submitted on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                        <p>Please respond promptly to maintain customer satisfaction.</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''
        
        return html_template

    def send_sales_enquiry(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send sales enquiry email to the sales team"""
        try:
            # Create message
            message = MIMEMultipart('alternative')
            message['Subject'] = f"🔔 New Sales Enquiry from {form_data.get('name')} - {form_data.get('company')}"
            message['From'] = formataddr((self.from_name, self.from_email))
            message['To'] = self.to_email
            message['Reply-To'] = form_data.get('email')

            # Add priority headers
            message['X-Priority'] = '1'
            message['X-MSMail-Priority'] = 'High'
            message['Importance'] = 'high'

            # Create HTML content
            html_content = self.generate_email_html(form_data)
            html_part = MIMEText(html_content, 'html')
            message.attach(html_part)

            logger.info(f"📧 Attempting to send email from {self.from_email} to {self.to_email}")
            logger.info(f"Subject: {message['Subject']}")

            # Send email
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            if self.smtp_secure:
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port, context=context)
            else:
                server = smtplib.SMTP(self.smtp_host, self.smtp_port)
                server.starttls(context=context)

            server.login(self.smtp_user, self.smtp_pass)
            text = message.as_string()
            server.sendmail(self.from_email, self.to_email, text)
            server.quit()

            message_id = f"python-{datetime.now().timestamp()}"
            logger.info(f"✅ Email sent successfully: {message_id}")

            return {
                'success': True,
                'messageId': message_id,
                'message': 'Email sent successfully'
            }

        except Exception as error:
            logger.error(f"❌ Failed to send email: {str(error)}")

            # Log detailed error information
            if hasattr(error, 'smtp_code'):
                logger.error(f"SMTP Code: {error.smtp_code}")
            if hasattr(error, 'smtp_error'):
                logger.error(f"SMTP Error: {error.smtp_error}")

            raise error

    def send_confirmation_email(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send confirmation email to the customer"""
        try:
            name = form_data.get('name', '')
            company = form_data.get('company', '')
            products = form_data.get('products', '')
            customer_email = form_data.get('email', '')

            confirmation_html = f'''
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Thank You for Your Enquiry</title>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #000000, #333333); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                    .footer {{ text-align: center; margin-top: 20px; color: #666; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Thank You!</h1>
                        <p>Atandra Energy Private Limited</p>
                    </div>
                    <div class="content">
                        <h2>Dear {name},</h2>
                        <p>Thank you for your enquiry about our energy solutions. We have received your request and our sales team will get back to you within 24 hours.</p>

                        <h3>Your Enquiry Details:</h3>
                        <p><strong>Company:</strong> {company}</p>
                        <p><strong>Products of Interest:</strong> {products}</p>

                        <p>In the meantime, feel free to explore our comprehensive product catalogue and learn more about our innovative energy management solutions.</p>

                        <div class="footer">
                            <p><strong>Contact Information:</strong></p>
                            <p>📧 <EMAIL></p>
                            <p>📞 +91 95000 97966</p>
                            <p>📍 No.5, Kumaran St, Pazhvanthangal, Chennai, Tamil Nadu, India, 600114</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            '''

            # Create message
            message = MIMEMultipart('alternative')
            message['Subject'] = 'Thank you for your enquiry - Atandra Energy'
            message['From'] = formataddr((self.from_name, self.from_email))
            message['To'] = customer_email
            message['X-Priority'] = '3'
            message['X-MSMail-Priority'] = 'Normal'

            # Create HTML content
            html_part = MIMEText(confirmation_html, 'html')
            message.attach(html_part)

            # Send email
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE

            if self.smtp_secure:
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port, context=context)
            else:
                server = smtplib.SMTP(self.smtp_host, self.smtp_port)
                server.starttls(context=context)

            server.login(self.smtp_user, self.smtp_pass)
            text = message.as_string()
            server.sendmail(self.from_email, customer_email, text)
            server.quit()

            message_id = f"python-confirmation-{datetime.now().timestamp()}"
            logger.info(f"✅ Confirmation email sent successfully: {message_id}")

            return {
                'success': True,
                'messageId': message_id,
                'message': 'Confirmation email sent successfully'
            }

        except Exception as error:
            logger.error(f"❌ Failed to send confirmation email: {str(error)}")
            raise error

# Create singleton instance
email_service = EmailService()
