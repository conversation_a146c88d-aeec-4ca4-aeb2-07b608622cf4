import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>R<PERSON>, Zap, Shield, Gauge, <PERSON>ting<PERSON>, Star, Award } from 'lucide-react';

const ProductsSection = () => {
  const products = [
    {
      icon: Gauge,
      title: 'Test & Measurement',
      description: 'Advanced diagnostic tools and precision measurement equipment for comprehensive power analysis',
      features: ['Digital Power Analyzers', 'Energy Meters', 'Load Banks', 'Harmonic Analyzers'],
      image: 'https://images.pexels.com/photos/8728561/pexels-photo-8728561.jpeg?auto=compress&cs=tinysrgb&w=800',
      color: 'from-purple-500 to-purple-700',
      bgColor: 'from-purple-50 to-purple-100',
      stats: { accuracy: '99.9%', range: '0.1-1000A' }
    },
    {
      icon: Zap,
      title: 'Online UPS Systems',
      description: 'Reliable uninterrupted power supply solutions for mission-critical applications',
      features: ['True Online Topology', '24/7 Monitoring', 'Remote Management', 'Scalable Design'],
      image: 'https://images.pexels.com/photos/442154/pexels-photo-442154.jpeg?auto=compress&cs=tinysrgb&w=800',
      color: 'from-blue-500 to-blue-700',
      bgColor: 'from-blue-50 to-blue-100',
      stats: { uptime: '99.99%', efficiency: '96%' }
    },
    {
      icon: Settings,
      title: 'Power Conditioners',
      description: 'Advanced servo stabilizers ensuring optimal voltage regulation for sensitive equipment',
      features: ['Servo Motor Drive', 'LCD Display', 'Overload Protection', 'Wide Input Range'],
      image: 'https://images.pexels.com/photos/159298/gears-cogs-machine-machinery-159298.jpeg?auto=compress&cs=tinysrgb&w=800',
      color: 'from-green-500 to-green-700',
      bgColor: 'from-green-50 to-green-100',
      stats: { accuracy: '±1%', response: '<1 sec' }
    },
    {
      icon: Shield,
      title: 'Static Voltage Regulators',
      description: 'High-performance static regulation systems for industrial and commercial applications',
      features: ['Microprocessor Control', 'Fast Response', 'High Efficiency', 'Compact Design'],
      image: 'https://images.pexels.com/photos/257736/pexels-photo-257736.jpeg?auto=compress&cs=tinysrgb&w=800',
      color: 'from-orange-500 to-orange-700',
      bgColor: 'from-orange-50 to-orange-100',
      stats: { efficiency: '98%', stability: '±0.5%' }
    }
  ];

  return (
    <section id="products" className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-6 font-['Open_Sans']">
            <Star className="h-4 w-4" />
            <span>Our Product Range</span>
          </div>
          
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 mb-6 font-['Open_Sans']">
            Power Solutions for
            <span className="block text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text">
              Every Need
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto font-['Open_Sans'] leading-relaxed">
            From precision measurement to uninterrupted power supply, discover our comprehensive range of power management solutions designed for reliability and performance.
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {products.map((product, index) => (
            <motion.div
              key={index}
              className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
            >
              {/* Background Image */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className={`absolute inset-0 bg-gradient-to-br ${product.color} opacity-90`}></div>
                
                {/* Floating Icon */}
                <div className="absolute top-6 left-6">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-2xl border border-white/30">
                    <product.icon className="h-8 w-8 text-white" />
                  </div>
                </div>

                {/* Stats */}
                <div className="absolute top-6 right-6 text-right">
                  {Object.entries(product.stats).map(([key, value], idx) => (
                    <div key={idx} className="mb-2">
                      <div className="text-2xl font-bold text-white font-['Open_Sans']">{value}</div>
                      <div className="text-sm text-white/80 capitalize font-['Open_Sans']">{key}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 font-['Open_Sans'] group-hover:text-blue-600 transition-colors">
                  {product.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed font-['Open_Sans']">
                  {product.description}
                </p>

                {/* Features */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                  {product.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${product.color}`}></div>
                      <span className="text-sm text-gray-600 font-medium font-['Open_Sans']">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <motion.button
                  className={`group/btn w-full bg-gradient-to-r ${product.color} text-white px-6 py-4 rounded-2xl font-semibold transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl font-['Open_Sans']`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>Learn More</span>
                  <ArrowRight className="h-5 w-5 transform group-hover/btn:translate-x-1 transition-transform" />
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 cursor-pointer">
            <Award className="h-6 w-6" />
            <span className="text-lg font-semibold font-['Open_Sans']">Need Custom Solutions? Let's Talk!</span>
            <ArrowRight className="h-6 w-6" />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductsSection;