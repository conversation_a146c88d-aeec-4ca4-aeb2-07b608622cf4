# 🔧 Troubleshooting "Network error: Failed to fetch"

## ✅ Python Backend Status: WORKING

Your Python backend is running successfully and processing requests correctly. The issue is likely a connection problem between your frontend and backend.

## 🔍 Quick Diagnosis

### 1. Check Backend Status
✅ **Python backend is running on http://localhost:5007**
✅ **Health endpoint working**: http://localhost:5007/health
✅ **Form submission working**: http://localhost:5007/api/email/send-enquiry
✅ **CORS configured for port 8080** (your Vite frontend)

### 2. Check Frontend Configuration
Your frontend should be making requests to: `http://localhost:5007`

In `src/pages/contact/Sales.tsx`, the API_BASE_URL is configured as:
```javascript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 
                    process.env.REACT_APP_API_URL || 
                    'http://localhost:5007';
```

## 🚀 Solutions

### Solution 1: Verify Backend is Running
1. Open a new terminal
2. Navigate to `backend_python`
3. Run: `start.bat` (Windows) or `./start.sh` (Linux/Mac)
4. You should see:
   ```
   🚀 Server starting on http://0.0.0.0:5007
   📧 Email service: smtp.mail.yahoo.com:587
   🌐 Environment: development
   🔒 CORS enabled for: http://localhost:8080
   ```

### Solution 2: Test Backend Manually
Open your browser and visit:
- http://localhost:5007/health
- http://localhost:5007/api/email/health

You should see JSON responses.

### Solution 3: Check Frontend Port
Your frontend runs on port 8080 (from vite.config.ts). Make sure:
1. Frontend is running: `npm run dev`
2. Frontend is accessible at: http://localhost:8080
3. Backend CORS is configured for port 8080 ✅ (already done)

### Solution 4: Clear Browser Cache
1. Open Developer Tools (F12)
2. Right-click refresh button → "Empty Cache and Hard Reload"
3. Or use Ctrl+Shift+R

### Solution 5: Check Network Tab
1. Open Developer Tools (F12)
2. Go to Network tab
3. Submit the form
4. Look for the request to `/api/email/send-enquiry`
5. Check if it shows:
   - **Status**: Should be 200
   - **Response**: Should show success message
   - **Headers**: Should include CORS headers

## 🔧 Advanced Troubleshooting

### Check CORS Headers
The backend should return these headers:
```
Access-Control-Allow-Origin: http://localhost:8080
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
```

### Test with curl (if available)
```bash
curl -X POST http://localhost:5007/api/email/send-enquiry \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:8080" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "company": "Test Company",
    "designation": "Manager",
    "city": "Chennai",
    "mobile": "+91 9876543210",
    "pincode": "600001",
    "products": "measure",
    "remarks": "Test",
    "requestDemo": true,
    "requestCallback": false,
    "sendDetails": true,
    "sendUpdates": false
  }'
```

### Check Environment Variables
Make sure your frontend doesn't have conflicting environment variables:
- `NEXT_PUBLIC_API_URL` should be empty or `http://localhost:5007`
- `REACT_APP_API_URL` should be empty or `http://localhost:5007`

## 🎯 Most Likely Solutions

1. **Backend not running**: Start the Python backend with `start.bat`
2. **Port conflict**: Make sure no other service is using port 5007
3. **Browser cache**: Clear cache and hard reload
4. **Firewall/Antivirus**: Temporarily disable to test

## 📞 Quick Test

1. **Start Python backend**:
   ```bash
   cd backend_python
   start.bat
   ```

2. **Test in browser**: Visit http://localhost:5007/health

3. **Test form submission**: Try submitting your form again

## ✅ Expected Behavior

When working correctly:
1. Form submits without "Network error"
2. You see success message: "Your enquiry has been received successfully..."
3. Backend logs show the form submission
4. Response includes `emailSent: false` (due to SMTP issue, but form processing works)

## 🆘 If Still Not Working

1. **Check Windows Firewall**: Allow Python through firewall
2. **Check Antivirus**: Temporarily disable real-time protection
3. **Try different port**: Change backend port in `.env` file
4. **Restart computer**: Sometimes helps with port conflicts

The Python backend is working perfectly - the issue is just getting your frontend to connect to it!
