import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Building, 
  Hospital, 
  Computer, 
  Factory,
  Database,
  Network,
  FileText,
  Mail,
  Activity,
  Settings,
  Microscope,
  Cpu
} from 'lucide-react';

const SXSeriesOverview = () => {
  return (
    <PageLayout
      title="SX Series Overview"
      subtitle="10 kVA to 120 kVA - Superior Protection with Built-in Isolation Transformers"
      category="protect"
    >
      <div className="font-['Open_Sans']">
        {/* Hero Section - Image Left, Content Right */}
        <div className="relative py-6 md:py-8 overflow-hidden bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
              {/* Product Image - Left Side */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative order-1 lg:order-1"
              >
                <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg">
                  <motion.img
                    src="/UPS/2-removebg-preview.png"
                    alt="SX Series UPS"
                    className="w-full max-w-xs mx-auto drop-shadow-lg"
                    animate={{
                      y: [0, -5, 0],
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 4,
                      ease: "easeInOut"
                    }}
                  />
                </div>
              </motion.div>

              {/* Content - Right Side */}
              <motion.div
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="space-y-4 order-2 lg:order-2 font-['Open_Sans']"
              >
                <div className="inline-block bg-blue-600 text-white px-3 py-1.5 rounded-full text-sm font-semibold font-['Open_Sans']">
                  KRYKARD SX Series
                </div>

                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white leading-tight font-['Open_Sans']">
                  Superior Protection with <span className="text-blue-600">Electrical Isolation</span>
                </h1>

                <p className="text-base md:text-lg text-gray-600 dark:text-gray-400 leading-relaxed font-['Open_Sans']">
                  The SX Series combines online double conversion technology with built-in isolation transformers, providing superior protection against electrical disturbances and ensuring clean, stable power for the most demanding applications. Features advanced DSP control and comprehensive monitoring capabilities.
                </p>

                <div className="flex flex-wrap gap-3">
                  <Link to="/contact/sales">
                    <motion.button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2.5 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans'] font-semibold"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Mail className="h-4 w-4" />
                      Request Quote
                    </motion.button>
                  </Link>

                  <Link to="/protect/ups/sx-series">
                    <motion.button
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-5 py-2.5 rounded-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans'] font-semibold"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FileText className="h-4 w-4" />
                      View Technical Details
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div className="py-6 md:py-8 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Precision Applications
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Engineered for environments where electrical isolation and superior power quality are critical requirements
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  icon: <Factory className="h-8 w-8 text-blue-600" />,
                  title: "Industrial Automation",
                  description: "Perfect for industrial environments requiring electrical isolation and superior power quality for automated control systems.",
                  applications: ["Industrial Automation", "Control Systems", "Manufacturing Equipment"]
                },
                {
                  icon: <Hospital className="h-8 w-8 text-blue-600" />,
                  title: "Medical Imaging Equipment",
                  description: "Essential for medical facilities with sensitive imaging equipment requiring the highest level of power protection and electrical isolation.",
                  applications: ["Medical Imaging", "Diagnostic Equipment", "Laboratory Systems"]
                },
                {
                  icon: <Microscope className="h-8 w-8 text-blue-600" />,
                  title: "Research Laboratory Instruments",
                  description: "Ideal for research institutions with precision instruments that demand clean, stable power and electrical isolation.",
                  applications: ["Research Instruments", "Laboratory Equipment", "Precision Machinery"]
                }
              ].map((useCase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2.5 rounded-lg">
                      {useCase.icon}
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white font-['Open_Sans']">
                      {useCase.title}
                    </h3>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4 font-['Open_Sans']">
                    {useCase.description}
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {useCase.applications.map((app, i) => (
                      <span
                        key={i}
                        className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2.5 py-1 rounded-full text-sm font-medium font-['Open_Sans']"
                      >
                        {app}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="py-8 md:py-10 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Need Precision Power Protection?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto font-['Open_Sans']">
                Get expert consultation for applications requiring electrical isolation and superior power quality
              </p>

              <div className="flex flex-wrap justify-center gap-3">
                <Link to="/contact/sales">
                  <motion.button
                    className="bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 rounded-lg font-bold shadow-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans']"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Mail className="h-4 w-4" />
                    Get Precision Quote
                  </motion.button>
                </Link>

                <Link to="/protect/ups/sx-series">
                  <motion.button
                    className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-3 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 font-['Open_Sans']"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FileText className="h-4 w-4" />
                    View Technical Specs
                    <ArrowRight className="h-4 w-4" />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default SXSeriesOverview;
