import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Building, 
  Hospital, 
  Computer, 
  Factory,
  Database,
  Network,
  FileText,
  Mail,
  Activity,
  Settings,
  Cloud,
  Server
} from 'lucide-react';

const EH33SmallSeriesOverview = () => {
  // Use cases with detailed descriptions
  const useCases = [
    {
      icon: <Database className="h-8 w-8 text-blue-600" />,
      title: "Data Center Operations",
      description: "Ideal for modern data centers requiring high-density power protection with advanced parallel operation capabilities for maximum uptime. Designed for mission-critical applications where downtime is not an option.",
      applications: ["Server Farms", "Network Operations Centers", "Cloud Infrastructure", "Data Processing Centers"],
      industries: ["Data Centers", "Cloud Service Providers", "IT Infrastructure"]
    },
    {
      icon: <Server className="h-8 w-8 text-blue-600" />,
      title: "Server Room Infrastructure",
      description: "Perfect for server rooms needing scalable power protection with comprehensive monitoring and remote management features. Ensures continuous operation of critical IT systems.",
      applications: ["Server Racks", "Network Infrastructure", "Storage Systems", "Backup Systems"],
      industries: ["Server Rooms", "IT Infrastructure", "Data Processing"]
    },
    {
      icon: <Network className="h-8 w-8 text-blue-600" />,
      title: "Telecommunications Equipment",
      description: "Essential for telecom infrastructure requiring reliable power backup with SNMP communication for seamless network integration. Maintains communication continuity.",
      applications: ["Telecom Equipment", "Network Switches", "Communication Systems", "Base Stations"],
      industries: ["Telecommunications", "Network Operations", "Communication Services"]
    },
    {
      icon: <Cloud className="h-8 w-8 text-blue-600" />,
      title: "Cloud Service Infrastructure",
      description: "Provides reliable power protection for cloud service providers requiring high availability and scalable power solutions for their infrastructure.",
      applications: ["Cloud Servers", "Virtual Infrastructure", "Distributed Systems", "Edge Computing"],
      industries: ["Cloud Services", "SaaS Providers", "Edge Computing"]
    }
  ];

  // Key features with benefits
  const keyFeatures = [
    {
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      title: "High-Frequency Technology",
      description: "Advanced high-frequency design delivers superior efficiency and reliability for data centers and server environments."
    },
    {
      icon: <Activity className="h-6 w-6 text-blue-600" />,
      title: "Parallel Operation Capability",
      description: "Advanced parallel operation capability allows multiple units to work together for redundancy and scalability up to 8 units."
    },
    {
      icon: <Network className="h-6 w-6 text-blue-600" />,
      title: "SNMP Communication",
      description: "Comprehensive SNMP communication enables remote management and seamless integration into existing network infrastructure."
    },
    {
      icon: <Shield className="h-6 w-6 text-blue-600" />,
      title: "Emergency Power Off",
      description: "Built-in emergency power off functionality provides additional safety features for critical environments."
    }
  ];

  // Industries served
  const industries = [
    "Data Centers", "Server Rooms", "IT Infrastructure", "Telecommunications", 
    "Cloud Service Providers", "Network Operations", "Edge Computing", "Communication Services"
  ];

  return (
    <PageLayout
      title="EH-33 Series (Small) Overview"
      subtitle="10 kVA to 60 kVA - High-Density Power Protection for Data Centers"
      category="protect"
    >
      <div className="font-['Open_Sans']">
        {/* Hero Section */}
        <div className="relative py-12 md:py-16 overflow-hidden bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
              {/* Content */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-6"
              >
                <div className="inline-block bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  KRYKARD EH-33 Series (Small)
                </div>
                
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
                  High-Density <span className="text-blue-600">Data Center Protection</span>
                </h1>
                
                <p className="text-lg md:text-xl text-gray-600 dark:text-gray-400 leading-relaxed">
                  The EH-33 Small Series utilizes advanced high-frequency technology to deliver maximum efficiency and reliability for data centers and server rooms. Features parallel operation capability and comprehensive communication options for seamless integration into existing infrastructure.
                </p>

                <div className="flex flex-wrap gap-4">
                  <Link to="/contact/sales">
                    <motion.button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Mail className="h-5 w-5" />
                      Request Quote
                    </motion.button>
                  </Link>
                  
                  <Link to="/protect/ups/eh-33-small-series">
                    <motion.button
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg flex items-center gap-2 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FileText className="h-5 w-5" />
                      View Technical Details
                    </motion.button>
                  </Link>
                </div>
              </motion.div>

              {/* Product Image */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200/30 to-transparent rounded-full blur-3xl"></div>
                <motion.img
                  src="/UPS/6-removebg-preview.png"
                  alt="EH-33 Small Series UPS"
                  className="relative z-10 w-full max-w-lg mx-auto drop-shadow-2xl"
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 4,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            </div>
          </div>
        </div>

        {/* Key Features Section */}
        <div className="py-16 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Advanced Data Center Technology
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Built for modern data centers and server environments requiring high-density power protection
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {keyFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Mission-Critical Applications
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Discover how the EH-33 Small Series protects critical infrastructure across various industries
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {useCases.map((useCase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                      {useCase.icon}
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {useCase.title}
                    </h3>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-6">
                    {useCase.description}
                  </p>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                        Typical Applications:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {useCase.applications.map((app, i) => (
                          <span
                            key={i}
                            className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {app}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                        Industries:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {useCase.industries.map((industry, i) => (
                          <span
                            key={i}
                            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {industry}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EH33SmallSeriesOverview;
