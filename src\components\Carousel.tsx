import React, { PropsWithChildren, useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';

interface CarouselProps {
  images: string[];
  className?: string;
}

const Carousel: React.FC<PropsWithChildren<CarouselProps>> = ({ images, className = '' }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [selectedIndex, setSelectedIndex] = useState(0);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    const onSelect = () => setSelectedIndex(emblaApi.selectedScrollSnap());
    emblaApi.on('select', onSelect);
    onSelect();
    return () => { emblaApi.off('select', onSelect); };
  }, [emblaApi]);

  // Auto-scroll every 10 seconds
  useEffect(() => {
    if (!emblaApi) return;
    const interval = setInterval(() => {
      const nextIndex = (selectedIndex + 1) % images.length;
      emblaApi.scrollTo(nextIndex);
    }, 10000); // 10 seconds
    return () => clearInterval(interval);
  }, [emblaApi, selectedIndex, images.length]);

  return (
    <div className={`embla ${className} relative`}>
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {images.map((src, idx) => (
            <div className="embla__slide" key={idx}>
              <img src={src} alt="carousel" className="w-full h-full object-contain rounded-2xl shadow-2xl" />
            </div>
          ))}
        </div>
      </div>
      {/* Arrow Buttons */}
      {images.length > 1 && (
        <>
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow border border-green-200 flex items-center justify-center"
            style={{ minWidth: 32, minHeight: 32 }}
            onClick={scrollPrev}
            aria-label="Previous slide"
            type="button"
          >
            {/* Left Arrow SVG */}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 15L8 10L13 5" stroke="#059669" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow border border-green-200 flex items-center justify-center"
            style={{ minWidth: 32, minHeight: 32 }}
            onClick={scrollNext}
            aria-label="Next slide"
            type="button"
          >
            {/* Right Arrow SVG */}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 5L12 10L7 15" stroke="#059669" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </>
      )}
      <div className="flex justify-center mt-2 gap-2 hidden sm:flex">
        {images.map((_, idx) => (
          <button
            key={idx}
            className={`
              w-2 h-2 md:w-2.5 md:h-2.5 rounded-full
              ${selectedIndex === idx ? 'bg-green-600' : 'bg-green-200'}
            `}
            onClick={() => scrollTo(idx)}
            aria-label={`Go to slide ${idx + 1}`}
          />
        ))}
      </div>
      <style>{`
        .embla { position: relative; height: 100%; }
        .embla__viewport { overflow: hidden; width: 100%; height: 100%; }
        .embla__container { display: flex; height: 100%; }
        .embla__slide { flex: 0 0 100%; min-width: 0; height: 100%; }
      `}</style>
    </div>
  );
};

export default Carousel;
