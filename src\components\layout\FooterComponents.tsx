import { FC } from "react";
import { motion } from "framer-motion";
import {
  FaLinkedin,
  FaFacebook,
  FaYoutube,
  FaPhoneAlt,
  FaEnvelope,
  FaMapMarkerAlt,
  FaArrowRight
} from "react-icons/fa";

// Clean Footer Link Component
interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
}

export const FooterLink: FC<FooterLinkProps> = ({ href, children }) => {
  return (
    <motion.a
      href={href}
      className="text-gray-300 hover:text-white transition-colors duration-300 text-base block py-1 font-['Open_Sans']"
      whileHover={{ x: 4 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      {children}
    </motion.a>
  );
};

// Professional Social Button
interface SocialButtonProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  color: string;
}

export const SocialButton: FC<SocialButtonProps> = ({ href, icon, label, color }) => {
  return (
    <motion.a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className={`w-10 h-10 rounded-lg ${color} flex items-center justify-center transition-all duration-300 hover:scale-105`}
      whileHover={{ y: -2 }}
      whileTap={{ scale: 0.95 }}
      aria-label={label}
    >
      {icon}
    </motion.a>
  );
};

// Main Professional Footer Component
const ProfessionalFooter: FC = () => {
  const currentYear = new Date().getFullYear();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  return (
    <footer className="bg-slate-900 py-16">
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="max-w-7xl mx-auto"
        >
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            
            {/* Company Information */}
            <motion.div variants={itemVariants} className="lg:col-span-1">
              <div className="mb-6">
                <img
                  src="/unnamed.png"
                  alt="KRYKARD Logo"
                  className="h-12 w-auto object-contain mb-4"
                  style={{ filter: 'brightness(0) saturate(100%) invert(1)' }}
                />
              </div>
              
                <p className="text-gray-300 leading-relaxed mb-4 text-base font-['Open_Sans']">
                India's leading power conditioning brand with over 40+ years of expertise 
                in Power & Energy Management solutions. Trusted by thousands of customers 
                across industries.
                </p>
{/*               
              <div className="space-y-2 text-sm text-gray-400 font-['Open_Sans']">
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                  ISO 9001:2015 Certified
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                  Pan-India Service Network
                </div>
                <div className="flex items-center">
                  <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                  24/7 Technical Support
                </div>
              </div>
               */}
              
              
              <motion.a
                href="/about/company"
                className="inline-flex items-center gap-2 text-blue-400 hover:text-white transition-colors duration-300 text-sm font-medium font-['Open_Sans'] mt-4"
                whileHover={{ x: 3 }}
              >
                Learn More
                <FaArrowRight className="text-xs" />
              </motion.a>
            </motion.div>

            {/* Products */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-white mb-6 font-['Open_Sans']">Products</h3>
              <ul className="space-y-2 font-['Open_Sans']">
                <li><FooterLink href="/measure">Testing & Measurement</FooterLink></li>
                <li><FooterLink href="/protect/ups">Online UPS</FooterLink></li>
                <li><FooterLink href="/protect/servo-stabilizers">Power Conditioners</FooterLink></li>
                <li><FooterLink href="/protect/static-stabilizers">Static Stabilizers</FooterLink></li>
                <li><FooterLink href="/measure/power-quality-analyzers">Power Quality Analyzers</FooterLink></li>
              </ul>
            </motion.div>

            {/* Quick Links */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-white mb-6 font-['Open_Sans']">Quick Links</h3>
              <ul className="space-y-2 font-['Open_Sans']">
                <li><FooterLink href="/">Home</FooterLink></li>
                <li><FooterLink href="/about">About</FooterLink></li>
                <li><FooterLink href="/contact/service">Service Care</FooterLink></li>
                <li><FooterLink href="/contact">Contact Us</FooterLink></li>
                <li><FooterLink href="/contact/service">Support</FooterLink></li>
              </ul>
            </motion.div>

            {/* Contact Information */}
            <motion.div variants={itemVariants}>
              <h3 className="text-lg font-semibold text-white mb-6 font-['Open_Sans']">Contact Us</h3>
              
              <address className="not-italic space-y-4 text-gray-300 text-sm font-['Open_Sans']">
                <div className="flex items-start">
                  <FaMapMarkerAlt className="h-4 w-4 text-blue-400 mt-1 mr-3 flex-shrink-0" />
                  <p className="leading-relaxed">
                    No.5, Kumaran St,<br />
                    Pazhavanthangal,<br />
                    Chennai - 600 114,<br />
                    Tamil Nadu, India
                  </p>
                </div>

                <a 
                  href="tel:+************" 
                  className="flex items-center text-gray-300 hover:text-white transition-colors duration-300"
                >
                  <FaPhoneAlt className="h-4 w-4 text-blue-400 mr-3 flex-shrink-0" />
                  <span>+91 95000 97966</span>
                </a>

                <a 
                  href="mailto:<EMAIL>" 
                  className="flex items-center text-gray-300 hover:text-white transition-colors duration-300"
                >
                  <FaEnvelope className="h-4 w-4 text-blue-400 mr-3 flex-shrink-0" />
                  <span><EMAIL></span>
                </a>
              </address>

              {/* Social Media */}
              <div className="flex space-x-3 mt-6">
                <SocialButton
                  href="#"
                  icon={<FaLinkedin className="h-5 w-5 text-white" />}
                  label="LinkedIn"
                  color="bg-blue-600 hover:bg-blue-700"
                />
                <SocialButton
                  href="#"
                  icon={<FaFacebook className="h-5 w-5 text-white" />}
                  label="Facebook"
                  color="bg-blue-700 hover:bg-blue-800"
                />
                <SocialButton
                  href="#"
                  icon={<FaYoutube className="h-5 w-5 text-white" />}
                  label="YouTube"
                  color="bg-red-600 hover:bg-red-700"
                />
              </div>
            </motion.div>
          </div>

          {/* Footer Bottom */}
          <motion.div variants={itemVariants}>
            <div className="border-t border-slate-700 pt-8">
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div className="flex items-center space-x-3">
                  <img
                    src="/unnamed.png"
                    alt="Atandra Energy"
                    className="h-8 w-auto object-contain opacity-80"
                    style={{ filter: 'brightness(0) saturate(100%) invert(1)' }}
                  />
                  <span className="text-sm text-gray-400 font-['Open_Sans']">
                    Powered by Atandra Energy Pvt. Ltd.
                  </span>
                </div>

                <div className="text-sm text-gray-400 text-center md:text-right font-['Open_Sans']">
                  <p>© {currentYear} KRYKARD. All rights reserved.</p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  );
};

export default ProfessionalFooter;