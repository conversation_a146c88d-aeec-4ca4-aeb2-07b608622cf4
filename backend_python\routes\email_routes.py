"""
Email routes for handling form submissions
Replaces the Node.js email routes with Flask blueprint
"""

from flask import Blueprint, request, jsonify
from marshmallow import Schema, fields, ValidationError, validate
import logging
from datetime import datetime
import json

from services.email_service import email_service

logger = logging.getLogger(__name__)

# Create blueprint
email_bp = Blueprint('email', __name__)

class SalesFormSchema(Schema):
    """Validation schema for sales form data - matches Node.js Joi schema"""
    
    name = fields.Str(
        required=True,
        validate=validate.Length(min=2, max=100),
        error_messages={
            'required': 'Name is required',
            'validator_failed': 'Name must be between 2 and 100 characters long'
        }
    )
    
    email = fields.Email(
        required=True,
        error_messages={
            'required': 'Email is required',
            'invalid': 'Please provide a valid email address'
        }
    )
    
    company = fields.Str(
        required=True,
        validate=validate.Length(min=2, max=200),
        error_messages={
            'required': 'Company name is required',
            'validator_failed': 'Company name must be between 2 and 200 characters long'
        }
    )
    
    designation = fields.Str(
        required=True,
        validate=validate.Length(min=2, max=100),
        error_messages={
            'required': 'Designation is required',
            'validator_failed': 'Designation must be between 2 and 100 characters long'
        }
    )
    
    city = fields.Str(
        required=True,
        validate=validate.Length(min=2, max=100),
        error_messages={
            'required': 'City is required',
            'validator_failed': 'City must be between 2 and 100 characters long'
        }
    )
    
    mobile = fields.Str(
        required=True,
        validate=validate.Regexp(
            r'^[+]?[\d\s\-\(\)]{10,15}$',
            error='Please provide a valid mobile number'
        ),
        error_messages={
            'required': 'Mobile number is required'
        }
    )
    
    pincode = fields.Str(
        required=True,
        validate=validate.Regexp(
            r'^[\d]{4,10}$',
            error='Please provide a valid pincode'
        ),
        error_messages={
            'required': 'Pincode is required'
        }
    )
    
    products = fields.Str(
        required=True,
        validate=validate.OneOf(['measure', 'protect', 'conserve', 'consultation']),
        error_messages={
            'required': 'Please select a product category',
            'validator_failed': 'Please select a valid product category'
        }
    )
    
    remarks = fields.Str(
        missing='',
        validate=validate.Length(max=1000),
        error_messages={
            'validator_failed': 'Remarks cannot exceed 1000 characters'
        }
    )
    
    requestDemo = fields.Bool(missing=False)
    requestCallback = fields.Bool(missing=False)
    sendDetails = fields.Bool(missing=False)
    sendUpdates = fields.Bool(missing=False)

# Initialize schema
sales_form_schema = SalesFormSchema()

@email_bp.route('/send-enquiry', methods=['POST'])
def send_enquiry():
    """Handle form submission - matches Node.js POST /api/email/send-enquiry"""
    try:
        # Check if request has JSON data
        if not request.is_json:
            return jsonify({
                'success': False,
                'message': 'Content-Type must be application/json',
                'error': 'INVALID_CONTENT_TYPE'
            }), 400

        # Check if JSON data exists
        if request.json is None:
            return jsonify({
                'success': False,
                'message': 'Request body must contain valid JSON',
                'error': 'MISSING_JSON_DATA'
            }), 400

        logger.info(f'📧 Received form submission: {request.json}')

        # Validate request body
        try:
            validated_data = sales_form_schema.load(request.json)
        except ValidationError as err:
            validation_errors = []
            for field, messages in err.messages.items():
                if isinstance(messages, list):
                    for message in messages:
                        validation_errors.append({
                            'field': field,
                            'message': message
                        })
                else:
                    validation_errors.append({
                        'field': field,
                        'message': messages
                    })
            
            return jsonify({
                'success': False,
                'message': 'Validation failed',
                'errors': validation_errors
            }), 400
        
        # Check if email service is ready (but don't fail if SMTP is down)
        is_email_service_ready = email_service.verify_connection()
        if not is_email_service_ready:
            logger.warning('⚠️ SMTP connection failed, but continuing with form processing...')
        
        # Try to send enquiry email to sales team
        email_result = None
        email_sent = False
        
        if is_email_service_ready:
            try:
                email_result = email_service.send_sales_enquiry(validated_data)
                email_sent = True
                logger.info('✅ Sales enquiry email sent successfully')
                
                # Send confirmation email to customer (optional, don't fail if this fails)
                try:
                    email_service.send_confirmation_email(validated_data)
                    logger.info('✅ Confirmation email sent to customer')
                except Exception as confirmation_error:
                    logger.warning(f'⚠️ Failed to send confirmation email to customer: {str(confirmation_error)}')
                    # Don't fail the entire request if confirmation email fails
                    
            except Exception as email_error:
                logger.error(f'❌ Failed to send sales enquiry email: {str(email_error)}')
                email_sent = False
        
        # Log the form submission for manual processing if email failed
        if not email_sent:
            logger.info(f'📝 FORM SUBMISSION (Email service unavailable): {json.dumps(validated_data, indent=2)}')
            logger.info('⚠️ Please manually process this enquiry as email service is currently unavailable.')
        
        return jsonify({
            'success': True,
            'message': (
                'Your enquiry has been submitted successfully. Our sales team will contact you soon.'
                if email_sent else
                'Your enquiry has been received successfully. Our sales team will contact you soon. (Note: Email service is temporarily unavailable, but your request has been logged.)'
            ),
            'data': {
                'messageId': email_result.get('messageId') if email_result else f"manual-{int(datetime.now().timestamp() * 1000)}",
                'timestamp': datetime.now().isoformat(),
                'emailSent': email_sent
            }
        }), 200
        
    except Exception as error:
        logger.error(f'❌ Error processing form submission: {str(error)}')
        
        # Handle specific error types
        error_message = str(error)
        status_code = 500
        error_code = 'INTERNAL_SERVER_ERROR'
        
        if 'authentication' in error_message.lower() or 'auth' in error_message.lower():
            status_code = 503
            error_code = 'EMAIL_AUTH_FAILED'
            error_message = 'Email authentication failed. Please contact support.'
        elif 'connection' in error_message.lower():
            status_code = 503
            error_code = 'EMAIL_CONNECTION_FAILED'
            error_message = 'Unable to connect to email server. Please try again later.'
        else:
            error_message = 'An unexpected error occurred. Please try again later.'
        
        return jsonify({
            'success': False,
            'message': error_message,
            'error': error_code
        }), status_code

@email_bp.route('/health', methods=['GET'])
def email_health():
    """Email service health check - matches Node.js GET /api/email/health"""
    try:
        is_email_service_ready = email_service.verify_connection()
        
        return jsonify({
            'success': True,
            'emailService': {
                'status': 'connected' if is_email_service_ready else 'disconnected',
                'host': email_service.smtp_host,
                'port': email_service.smtp_port,
                'secure': email_service.smtp_secure
            },
            'timestamp': datetime.now().isoformat()
        }), 200
        
    except Exception as error:
        return jsonify({
            'success': False,
            'message': 'Email service health check failed',
            'error': str(error),
            'timestamp': datetime.now().isoformat()
        }), 503
