import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Zap,
  Shield,
  Plus,
  PlusCircle,
  Globe,
  Clock,
  ShieldCheck,
  Headphones,
  HelpCircle,
  BarChart3,
  FileText,
  Mail
} from "lucide-react";

// Specifications data
const specifications = [
  {
    category: "Input",
    items: [
      { label: "Voltage Range", value: "340-460V AC" },
      { label: "Frequency", value: "47-53 Hz" },
      { label: "Phase Options", value: "3φ" }
    ]
  },
  {
    category: "Output",
    items: [
      { label: "Regulation", value: "±1%" },
      { label: "Correction Speed", value: "20,000 V/Sec" },
      { label: "Efficiency", value: ">95%" }
    ]
  },
  {
    category: "Protection",
    items: [
      { label: "Overload Trip", value: "110% to 150%" },
      { label: "Surge Protection", value: "6kV" },
      { label: "Control", value: "DSP PWM" }
    ]
  }
];

// Completely Redesigned Technical Specifications Section
const TechnicalSpecificationsSection = () => {
  return (
    <section id="specifications" className="relative py-8 bg-gradient-to-br from-blue-50/50 via-white to-indigo-50/50 dark:from-gray-900/50 dark:via-gray-800/50 dark:to-blue-900/20">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-10 right-10 w-64 h-64 bg-blue-300/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 left-10 w-48 h-48 bg-indigo-300/10 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-4 font-['Open_Sans']">
            Technical Specifications
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-3 rounded-full"></div>
          <p className="text-center text-gray-800 dark:text-gray-200 max-w-3xl mx-auto mt-6 text-base font-medium leading-relaxed font-['Open_Sans']">
            Engineered for optimal performance across all applications with industry-leading precision
          </p>
        </div>

        {/* Mobile-First Responsive Specifications */}
        <div className="max-w-6xl mx-auto">
          {/* Mobile Layout: Single Column */}
          <div className="block md:hidden space-y-4">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-blue-100/50 dark:border-blue-900/30 overflow-hidden"
              >
                {/* Category Header */}
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4">
                  <div className="flex items-center">
                    <div className="bg-white/20 text-white p-2 rounded-lg mr-3">
                      {index === 0 ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      ) : index === 1 ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      )}
                    </div>
                    <h3 className="text-lg font-bold text-white font-['Open_Sans']">{spec.category}</h3>
                  </div>
                </div>

                {/* Specifications List */}
                <div className="p-4 space-y-3">
                  {spec.items.map((item, itemIndex) => (
                    <motion.div
                      key={itemIndex}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: 0.2 + (itemIndex * 0.1) }}
                      className="flex flex-col space-y-2 p-3 rounded-lg bg-blue-50/70 dark:bg-blue-900/30 border border-blue-100/50 dark:border-blue-800/30"
                    >
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-blue-500 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-800 dark:text-gray-200 font-medium text-sm font-['Open_Sans']">{item.label}</span>
                      </div>
                      <div className="ml-5">
                        <span className="font-bold text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 px-3 py-1 rounded-md text-sm font-['Open_Sans']">
                          {item.value}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Tablet Layout: Two Columns */}
          <div className="hidden md:block lg:hidden grid grid-cols-2 gap-6">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-blue-100/50 dark:border-blue-900/30 overflow-hidden"
              >
                {/* Category Header */}
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4">
                  <div className="flex items-center">
                    <div className="bg-white/20 text-white p-2 rounded-lg mr-3">
                      {index === 0 ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      ) : index === 1 ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      )}
                    </div>
                    <h3 className="text-lg font-bold text-white font-['Open_Sans']">{spec.category}</h3>
                  </div>
                </div>

                {/* Specifications List */}
                <div className="p-4 space-y-3">
                  {spec.items.map((item, itemIndex) => (
                    <motion.div
                      key={itemIndex}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: 0.2 + (itemIndex * 0.1) }}
                      className="flex flex-col space-y-2 p-3 rounded-lg bg-blue-50/70 dark:bg-blue-900/30 border border-blue-100/50 dark:border-blue-800/30"
                    >
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-blue-500 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-800 dark:text-gray-200 font-medium text-sm font-['Open_Sans']">{item.label}</span>
                      </div>
                      <div className="ml-5">
                        <span className="font-bold text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 px-2 py-1 rounded-md text-sm font-['Open_Sans']">
                          {item.value}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Desktop Layout: Three Columns */}
          <div className="hidden lg:grid grid-cols-3 gap-6">
            {specifications.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-blue-100/50 dark:border-blue-900/30 overflow-hidden hover:shadow-xl transition-all duration-300"
              >
                {/* Category Header */}
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4">
                  <div className="flex items-center">
                    <div className="bg-white/20 text-white p-3 rounded-lg mr-3 group-hover:scale-110 transition-transform duration-300">
                      {index === 0 ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      ) : index === 1 ? (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      )}
                    </div>
                    <h3 className="text-lg font-bold text-white font-['Open_Sans']">{spec.category}</h3>
                  </div>
                </div>

                {/* Specifications List */}
                <div className="p-6 space-y-4">
                  {spec.items.map((item, itemIndex) => (
                    <motion.div
                      key={itemIndex}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: 0.2 + (itemIndex * 0.1) }}
                      className="flex justify-between items-center p-3 rounded-lg bg-blue-50/70 dark:bg-blue-900/30 border border-blue-100/50 dark:border-blue-800/30 hover:bg-blue-100/80 dark:hover:bg-blue-900/50 transition-colors duration-200"
                    >
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-blue-500 mr-3"></div>
                        <span className="text-gray-800 dark:text-gray-200 font-medium text-base font-['Open_Sans']">{item.label}</span>
                      </div>
                      <span className="font-bold text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30 px-3 py-1 rounded-md text-base font-['Open_Sans']">
                        {item.value}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TechnicalSpecificationsSection; 