import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Building, 
  Hospital, 
  Computer, 
  Factory,
  Database,
  Network,
  FileText,
  Mail,
  Activity,
  Settings,
  TrendingUp,
  Users
} from 'lucide-react';

const EH33LargeSeriesOverview = () => {
  return (
    <PageLayout
      title="EH-33 Series (Large) Overview"
      subtitle="80 kVA to 200 kVA - Maximum Efficiency for Large-Scale Infrastructure"
      category="protect"
    >
      <div className="font-['Open_Sans']">
        {/* Hero Section */}
        <div className="relative py-12 md:py-16 overflow-hidden bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
              {/* Content */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-6"
              >
                <div className="inline-block bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  KRYKARD EH-33 Series (Large)
                </div>
                
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
                  Maximum Efficiency for <span className="text-blue-600">Large-Scale Infrastructure</span>
                </h1>
                
                <p className="text-lg md:text-xl text-gray-600 dark:text-gray-400 leading-relaxed">
                  The EH-33 Large Series represents the pinnacle of UPS technology with transformer-less design and high-frequency conversion. Engineered for large-scale infrastructure requiring maximum efficiency, reliability, and advanced power management capabilities.
                </p>

                <div className="flex flex-wrap gap-4">
                  <Link to="/contact/sales">
                    <motion.button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Mail className="h-5 w-5" />
                      Request Quote
                    </motion.button>
                  </Link>
                  
                  <Link to="/protect/ups/eh-33-large-series">
                    <motion.button
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg flex items-center gap-2 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FileText className="h-5 w-5" />
                      View Technical Details
                    </motion.button>
                  </Link>
                </div>
              </motion.div>

              {/* Product Image */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200/30 to-transparent rounded-full blur-3xl"></div>
                <motion.img
                  src="/UPS/2__1_-removebg-preview.png"
                  alt="EH-33 Large Series UPS"
                  className="relative z-10 w-full max-w-lg mx-auto drop-shadow-2xl"
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 4,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div className="py-16 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Enterprise-Level Applications
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Perfect for large-scale operations requiring the highest levels of power protection and efficiency
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <Database className="h-8 w-8 text-blue-600" />,
                  title: "Enterprise Data Centers",
                  description: "Perfect for large-scale data centers requiring maximum efficiency and N+X parallel redundancy for mission-critical operations.",
                  applications: ["Enterprise Data Centers", "Cloud Infrastructure", "Hyperscale Operations"]
                },
                {
                  icon: <Factory className="h-8 w-8 text-blue-600" />,
                  title: "Industrial Manufacturing",
                  description: "Essential for large manufacturing facilities requiring robust power protection for automated systems and production lines.",
                  applications: ["Industrial Manufacturing", "Automated Production", "Process Control"]
                },
                {
                  icon: <Hospital className="h-8 w-8 text-blue-600" />,
                  title: "Hospital Critical Infrastructure",
                  description: "Vital for hospitals requiring the highest level of power protection for life-support systems and critical medical equipment.",
                  applications: ["Hospital Infrastructure", "Medical Equipment", "Life Support Systems"]
                }
              ].map((useCase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-6">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                      {useCase.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                      {useCase.title}
                    </h3>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                    {useCase.description}
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {useCase.applications.map((app, i) => (
                      <span
                        key={i}
                        className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full text-sm font-medium"
                      >
                        {app}
                      </span>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="py-16 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Ready for Enterprise-Grade Protection?
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
                Get expert consultation for large-scale power protection solutions
              </p>

              <div className="flex flex-wrap justify-center gap-4">
                <Link to="/contact/sales">
                  <motion.button
                    className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-4 rounded-lg font-bold shadow-lg flex items-center gap-2 transition-all duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Mail className="h-5 w-5" />
                    Get Enterprise Quote
                  </motion.button>
                </Link>

                <Link to="/protect/ups/eh-33-large-series">
                  <motion.button
                    className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-lg font-bold flex items-center gap-2 transition-all duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FileText className="h-5 w-5" />
                    View Technical Specs
                    <ArrowRight className="h-5 w-5" />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EH33LargeSeriesOverview;
