import React from 'react';
import { motion } from 'framer-motion';
import { Globe, Users, Building, Award } from 'lucide-react';

const GlobalReachSection = () => {
  const globalStats = [
    {
      icon: Globe,
      value: "50+",
      label: "Countries Served",
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50 to-blue-100"
    },
    {
      icon: Building,
      value: "500+",
      label: "Global Partners",
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50 to-green-100"
    },
    {
      icon: Users,
      value: "1M+",
      label: "Global Customers",
      color: "from-purple-500 to-purple-600",
      bgColor: "from-purple-50 to-purple-100"
    },
    {
      icon: Award,
      value: "25+",
      label: "International Awards",
      color: "from-orange-500 to-orange-600",
      bgColor: "from-orange-50 to-orange-100"
    }
  ];

  return (
    <section id="global" className="py-20 bg-slate-900 relative overflow-hidden">

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6 font-['Open_Sans']">
            <Globe className="h-4 w-4" />
            <span>Global Presence</span>
          </div>
          
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black text-white mb-6 font-['Open_Sans']">
            Powering the World
            <span className="block text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
              One Solution at a Time
            </span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-4xl mx-auto font-['Open_Sans'] leading-relaxed">
            From our headquarters in Chennai to global markets across 50+ countries, we deliver world-class 
            power management solutions that keep businesses running smoothly worldwide.
          </p>
        </motion.div>

        {/* World Map Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="relative bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-2xl">
            {/* World Map Image */}
            <div className="relative h-96 rounded-2xl overflow-hidden">
              <img
                src="public/map.png"
                alt="Global Network Map"
                className="w-full h-full object-cover rounded-2xl"
              />

              {/* Overlay Text */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center bg-black/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                  <h3 className="text-2xl font-bold text-white mb-2 font-['Open_Sans']">Global Network</h3>
                  <p className="text-blue-300 font-['Open_Sans']">Connecting 50+ Countries</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Global Stats */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {globalStats.map((stat, index) => (
            <motion.div
              key={index}
              className={`bg-gradient-to-br ${stat.bgColor} rounded-2xl p-6 text-center border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 group`}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -5, scale: 1.05 }}
            >
              <div className={`inline-flex p-3 rounded-2xl bg-gradient-to-br ${stat.color} text-white mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                <stat.icon className="h-6 w-6" />
              </div>
              <div className={`text-3xl font-black bg-gradient-to-br ${stat.color} bg-clip-text text-transparent mb-2 font-['Open_Sans']`}>
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 font-semibold font-['Open_Sans']">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>


      </div>
    </section>
  );
};

export default GlobalReachSection;