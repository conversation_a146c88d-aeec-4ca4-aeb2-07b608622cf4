import React from 'react';
import { motion } from 'framer-motion';
import { Globe, MapPin, Users, Building, Award, TrendingUp } from 'lucide-react';

const GlobalReachSection = () => {
  const globalStats = [
    {
      icon: Globe,
      value: "50+",
      label: "Countries Served",
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50 to-blue-100"
    },
    {
      icon: Building,
      value: "500+",
      label: "Global Partners",
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50 to-green-100"
    },
    {
      icon: Users,
      value: "1M+",
      label: "Global Customers",
      color: "from-purple-500 to-purple-600",
      bgColor: "from-purple-50 to-purple-100"
    },
    {
      icon: Award,
      value: "25+",
      label: "International Awards",
      color: "from-orange-500 to-orange-600",
      bgColor: "from-orange-50 to-orange-100"
    }
  ];

  const regions = [
    {
      name: "Asia Pacific",
      countries: ["India", "Singapore", "Malaysia", "Thailand", "Philippines"],
      color: "from-blue-400 to-blue-600"
    },
    {
      name: "Middle East",
      countries: ["UAE", "Saudi Arabia", "Qatar", "Kuwait", "Oman"],
      color: "from-green-400 to-green-600"
    },
    {
      name: "Africa",
      countries: ["South Africa", "Nigeria", "Kenya", "Egypt", "Morocco"],
      color: "from-purple-400 to-purple-600"
    },
    {
      name: "Europe",
      countries: ["Germany", "UK", "France", "Netherlands", "Italy"],
      color: "from-orange-400 to-orange-600"
    }
  ];

  return (
    <section id="global" className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '40px 40px',
          animation: 'float 25s ease-in-out infinite'
        }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6 font-['Open_Sans']">
            <Globe className="h-4 w-4" />
            <span>Global Presence</span>
          </div>
          
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-black text-white mb-6 font-['Open_Sans']">
            Powering the World
            <span className="block text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
              One Solution at a Time
            </span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-4xl mx-auto font-['Open_Sans'] leading-relaxed">
            From our headquarters in Chennai to global markets across 50+ countries, we deliver world-class 
            power management solutions that keep businesses running smoothly worldwide.
          </p>
        </motion.div>

        {/* World Map Section */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="relative bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 shadow-2xl">
            {/* World Map Placeholder */}
            <div className="relative h-96 bg-gradient-to-br from-blue-900/30 to-purple-900/30 rounded-2xl flex items-center justify-center overflow-hidden">
              {/* Animated Globe */}
              <motion.div
                className="relative"
                animate={{ rotate: 360 }}
                transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
              >
                <Globe className="h-32 w-32 text-blue-400 opacity-80" />
              </motion.div>
              
              {/* Floating Connection Points */}
              <motion.div
                className="absolute top-1/4 left-1/4 w-4 h-4 bg-blue-400 rounded-full shadow-lg"
                animate={{ scale: [1, 1.5, 1], opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <motion.div
                className="absolute top-1/3 right-1/4 w-4 h-4 bg-green-400 rounded-full shadow-lg"
                animate={{ scale: [1, 1.5, 1], opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
              />
              <motion.div
                className="absolute bottom-1/3 left-1/3 w-4 h-4 bg-purple-400 rounded-full shadow-lg"
                animate={{ scale: [1, 1.5, 1], opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
              />
              <motion.div
                className="absolute bottom-1/4 right-1/3 w-4 h-4 bg-orange-400 rounded-full shadow-lg"
                animate={{ scale: [1, 1.5, 1], opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}
              />
              
              {/* Connection Lines */}
              <svg className="absolute inset-0 w-full h-full">
                <motion.path
                  d="M 25% 25% Q 50% 10% 75% 33%"
                  stroke="rgba(59, 130, 246, 0.5)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="5,5"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 3, repeat: Infinity }}
                />
                <motion.path
                  d="M 33% 67% Q 50% 90% 67% 75%"
                  stroke="rgba(168, 85, 247, 0.5)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="5,5"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 3, repeat: Infinity, delay: 1 }}
                />
              </svg>
              
              {/* Overlay Text */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center bg-black/20 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                  <h3 className="text-2xl font-bold text-white mb-2 font-['Open_Sans']">Global Network</h3>
                  <p className="text-blue-300 font-['Open_Sans']">Connecting 50+ Countries</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Global Stats */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {globalStats.map((stat, index) => (
            <motion.div
              key={index}
              className={`bg-gradient-to-br ${stat.bgColor} rounded-2xl p-6 text-center border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 group`}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -5, scale: 1.05 }}
            >
              <div className={`inline-flex p-3 rounded-2xl bg-gradient-to-br ${stat.color} text-white mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                <stat.icon className="h-6 w-6" />
              </div>
              <div className={`text-3xl font-black bg-gradient-to-br ${stat.color} bg-clip-text text-transparent mb-2 font-['Open_Sans']`}>
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 font-semibold font-['Open_Sans']">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Regional Presence */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          {regions.map((region, index) => (
            <motion.div
              key={index}
              className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ y: -5, scale: 1.02 }}
            >
              <div className={`inline-flex p-3 rounded-2xl bg-gradient-to-br ${region.color} text-white mb-4 shadow-lg`}>
                <MapPin className="h-6 w-6" />
              </div>
              <h4 className="text-lg font-bold text-white mb-4 font-['Open_Sans']">
                {region.name}
              </h4>
              <div className="space-y-2">
                {region.countries.map((country, idx) => (
                  <div key={idx} className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${region.color}`}></div>
                    <span className="text-gray-300 text-sm font-['Open_Sans']">{country}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-10px) rotate(120deg); }
          66% { transform: translateY(10px) rotate(240deg); }
        }
      `}</style>
    </section>
  );
};

export default GlobalReachSection;