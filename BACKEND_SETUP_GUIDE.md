# Backend Setup Guide for Sales Form Email Functionality

This guide will help you set up the backend server to handle sales form submissions and send emails using Node.js and <PERSON>demail<PERSON>.

## 📋 Overview

The backend consists of:
- **Express.js** server for handling API requests
- **Nodemailer** for sending emails via SMTP
- **Form validation** using Joi
- **Security features** including rate limiting and CORS
- **Email templates** for both sales team and customer confirmation

## 🚀 Quick Start

### Step 1: Navigate to Backend Directory
```bash
cd backend
```

### Step 2: Install Dependencies
```bash
npm install
```

### Step 3: Configure Environment Variables
```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your SMTP credentials
```

### Step 4: Configure SMTP Settings

Edit the `.env` file with your email provider settings:

#### For Gmail:
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>
```

#### For Outlook/Hotmail:
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>
```

### Step 5: Start the Server

#### Option A: Using Start Scripts (Recommended)
**Windows:**
```bash
start.bat
```

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

#### Option B: Using npm commands
**Development mode:**
```bash
npm run dev
```

**Production mode:**
```bash
npm start
```

## 🔧 Gmail Setup (Recommended)

1. **Enable 2-Factor Authentication:**
   - Go to your Google Account settings
   - Navigate to Security → 2-Step Verification
   - Enable 2FA if not already enabled

2. **Generate App Password:**
   - In Security settings, go to "App passwords"
   - Select "Mail" as the app
   - Generate a 16-character password
   - Use this password in `SMTP_PASS` (not your regular Gmail password)

3. **Update .env file:**
   ```env
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-16-character-app-password
   FROM_EMAIL=<EMAIL>
   ```

## 📧 How It Works

1. **User submits form** on the frontend (Sales page)
2. **Frontend sends data** to `http://localhost:5000/api/email/send-enquiry`
3. **Backend validates** the form data using Joi schema
4. **Email is sent** to the sales team with enquiry details
5. **Confirmation email** is sent to the customer
6. **Response is returned** to frontend with success/error status

## 🔍 Testing the Setup

### 1. Check Server Health
```bash
curl http://localhost:5000/health
```

### 2. Check Email Service Health
```bash
curl http://localhost:5000/api/email/health
```

### 3. Test Form Submission
```bash
curl -X POST http://localhost:5000/api/email/send-enquiry \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "company": "Test Company",
    "designation": "Manager",
    "city": "Chennai",
    "mobile": "+91 **********",
    "pincode": "600001",
    "products": "measure",
    "remarks": "Test enquiry",
    "requestDemo": true
  }'
```

## 🛡️ Security Features

- **Rate Limiting:** 5 requests per 15 minutes per IP
- **CORS Protection:** Only allows requests from configured frontend URL
- **Input Validation:** All form fields are validated using Joi schemas
- **Helmet Security:** Adds security headers to responses
- **Error Handling:** Secure error responses that don't leak sensitive information

## 📁 File Structure

```
backend/
├── package.json          # Dependencies and scripts
├── server.js            # Main server file
├── .env                 # Environment variables (create from .env.example)
├── .env.example         # Environment template
├── routes/
│   └── emailRoutes.js   # Email API routes
├── services/
│   └── emailService.js  # Email service with Nodemailer
├── start.bat           # Windows startup script
├── start.sh            # Linux/Mac startup script
└── README.md           # Detailed documentation
```

## 🔧 Troubleshooting

### Common Issues:

1. **"SMTP Authentication Failed"**
   - Check if you're using the correct app password (not regular password)
   - Verify SMTP credentials in .env file
   - Ensure 2FA is enabled for Gmail

2. **"Connection Timeout"**
   - Check firewall settings
   - Verify SMTP host and port
   - Try different SMTP provider

3. **"Rate Limit Exceeded"**
   - Wait 15 minutes for rate limit to reset
   - Adjust `RATE_LIMIT_MAX_REQUESTS` in .env

4. **"CORS Error"**
   - Ensure `FRONTEND_URL` in .env matches your frontend URL
   - Check if frontend is running on the correct port

### Logs:
The server provides detailed logs:
- ✅ Success operations
- ❌ Errors and failures  
- ⚠️ Warnings

## 🌐 Frontend Integration

The frontend form automatically connects to the backend when:
1. Backend server is running on `http://localhost:5000`
2. Frontend is configured to send requests to the correct endpoint
3. CORS is properly configured

## 📞 Support

If you encounter issues:
1. Check the server logs for detailed error messages
2. Verify all environment variables are correctly set
3. Test email service health endpoint
4. Ensure all dependencies are installed

The backend is now ready to handle sales form submissions and send emails to your specified address!
