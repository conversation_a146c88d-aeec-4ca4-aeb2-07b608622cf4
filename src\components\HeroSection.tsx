import React, { useState, useEffect } from 'react';
import { ArrowDown, Play, Shield } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const heroSlides = [
    {
      title: "Powering India's Future",
      subtitle: "Advanced Power & Energy Management Solutions",
      description: "40+ years of excellence in delivering reliable power conditioning solutions across India",
      cta: "Discover Solutions",
      bg: "/background_images/Slide_1.png",
      stats: [
        { label: "Years Experience", value: "40+" },
        { label: "Service Centers", value: "100+" },
        { label: "Happy Clients", value: "10K+" }
      ]
    },
    {
      title: "Uninterrupted Power Supply",
      subtitle: "Enterprise-Grade UPS Systems",
      description: "Protect your critical operations with our state-of-the-art UPS solutions",
      cta: "View UPS Range",
      bg: "/background_images/Slide_3.png",
      stats: [
        { label: "Uptime Guarantee", value: "99.9%" },
        { label: "Response Time", value: "24hrs" },
        { label: "Warranty", value: "5 Years" }
      ]
    },
    {
      title: "Voltage Stabilization",
      subtitle: "Precision Power Conditioning",
      description: "Advanced servo stabilizers ensuring optimal voltage for your equipment",
      cta: "Learn More",
      bg: "/background_images/Slide_4.png",
      stats: [
        { label: "Accuracy", value: "±1%" },
        { label: "Efficiency", value: "98%" },
        { label: "Load Range", value: "1-5000KVA" }
      ]
    },
    {
      title: "Advanced Testing & Measurement",
      subtitle: "Precision Diagnostic Tools",
      description: "Ensure optimal performance with our advanced testing and measurement equipment",
      cta: "Explore Products",
      bg: "/background_images/Slide_5.png",
      stats: [
        { label: "Accuracy", value: "99.9%" },
        { label: "Range", value: "0.1-1000A" }
        ]
    },
    {
      title: "Energy Conservation",
      subtitle: "Efficient Power Management",
      description: "Reduce your energy costs with our cutting-edge power management solutions",
      cta: "Contact Us",
      bg: "/background_images/Slide_6.png",
      stats: [
        { label: "Energy Savings", value: "30%" },
        { label: "ROI", value: "1-2 Years" }
        ]
    }

  ];


  // Auto-slide functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 6000);
    return () => clearInterval(timer);
  }, []);

  const scrollToProducts = () => {
    const productsSection = document.getElementById('products');
    if (productsSection) {
      productsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Images with Smooth Transitions */}
      <div className="absolute inset-0">
        {heroSlides.map((slide, index) => (
          <motion.div
            key={index}
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ 
              opacity: index === currentSlide ? 1 : 0,
              scale: index === currentSlide ? 1 : 1.1
            }}
            transition={{ 
              duration: 1.5,
              ease: "easeInOut"
            }}
          >
            <img
              src={slide.bg}
              alt={`${slide.title} Background`}
              className="w-full h-full object-cover"
              style={{ 
                filter: 'brightness(0.7)',
                transform: index === currentSlide ? 'scale(1)' : 'scale(1.05)'
              }}
            />
          </motion.div>
        ))}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-gray-800/70 to-blue-800/80"></div>
      </div>

      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px',
          animation: 'float 20s ease-in-out infinite'
        }}></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            {/* Badge */}
            <motion.div
              className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Shield className="h-5 w-5 text-blue-300" />
              <span className="text-white font-medium font-['Open_Sans']">India's #1 Power Solutions Provider</span>
            </motion.div>

            {/* Main Title */}
            <motion.h1
              className="text-4xl sm:text-5xl lg:text-7xl font-black text-white mb-6 leading-tight font-['Open_Sans']"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {heroSlides[currentSlide].title}
            </motion.h1>

            {/* Subtitle */}
            <motion.h2
              className="text-xl sm:text-2xl lg:text-3xl font-semibold text-blue-200 mb-6 font-['Open_Sans']"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              {heroSlides[currentSlide].subtitle}
            </motion.h2>

            {/* Description */}
            <motion.p
              className="text-lg sm:text-xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed font-['Open_Sans']"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              {heroSlides[currentSlide].description}
            </motion.p>

            {/* Stats */}
            <motion.div
              className="grid grid-cols-3 gap-6 sm:gap-8 mb-10"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              {heroSlides[currentSlide].stats.map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center bg-white/5 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white/10"
                  whileHover={{ scale: 1.05, backgroundColor: 'rgba(255,255,255,0.1)' }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2 font-['Open_Sans']">
                    {stat.value}
                  </div>
                  <div className="text-sm sm:text-base text-blue-200 font-medium font-['Open_Sans']">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <motion.button
                className="group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-3 font-['Open_Sans']"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>{heroSlides[currentSlide].cta}</span>
                <ArrowDown className="h-5 w-5 transform rotate-[-45deg] group-hover:translate-x-1 transition-transform" />
              </motion.button>
              
              <motion.button
                className="group bg-white/10 backdrop-blur-sm border border-white/30 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 hover:bg-white/20 flex items-center space-x-3 font-['Open_Sans']"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Play className="h-5 w-5" />
                <span>Watch Demo</span>
              </motion.button>
            </motion.div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Scroll Indicator - Centered */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-white/80 cursor-pointer z-20"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        onClick={scrollToProducts}
      >
        <span className="text-sm font-medium mb-2 hidden sm:block font-['Open_Sans']">Scroll</span>
        <ArrowDown className="h-6 w-6 animate-bounce" />
      </motion.div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `}</style>
    </div>
  );
};

export default HeroSection;