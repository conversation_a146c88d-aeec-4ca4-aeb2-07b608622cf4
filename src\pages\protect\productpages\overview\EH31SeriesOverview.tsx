import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Building, 
  Hospital, 
  Computer, 
  Factory,
  Database,
  TrendingUp,
  FileText,
  Mail,
  Activity,
  Settings
} from 'lucide-react';

const EH31SeriesOverview = () => {
  // Use cases with detailed descriptions
  const useCases = [
    {
      icon: <Database className="h-8 w-8 text-blue-600" />,
      title: "Enterprise Data Centers",
      description: "Designed for medium-sized data centers requiring three-phase input capability and superior power quality protection. Ensures maximum uptime for critical data operations.",
      applications: ["Server Rooms", "Data Processing Equipment", "Network Infrastructure", "Storage Systems"],
      industries: ["Data Centers", "Cloud Services", "IT Infrastructure"]
    },
    {
      icon: <Factory className="h-8 w-8 text-blue-600" />,
      title: "Manufacturing Control Systems",
      description: "Essential for manufacturing environments where power quality is critical for automated control systems and production equipment. Prevents costly production downtime.",
      applications: ["Manufacturing Control Systems", "Automated Production Lines", "Quality Control Systems", "Process Monitoring"],
      industries: ["Manufacturing", "Industrial Automation", "Process Control"]
    },
    {
      icon: <Hospital className="h-8 w-8 text-blue-600" />,
      title: "Healthcare Critical Systems",
      description: "Perfect for healthcare facilities requiring reliable power backup for life-critical medical equipment and patient monitoring systems. Ensures patient safety and care continuity.",
      applications: ["Medical Devices", "Patient Monitoring Systems", "Laboratory Equipment", "Diagnostic Systems"],
      industries: ["Healthcare Facilities", "Hospitals", "Medical Centers"]
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-blue-600" />,
      title: "Financial Trading Systems",
      description: "Provides uninterrupted power for financial trading systems and critical business operations where even milliseconds of downtime can result in significant losses.",
      applications: ["Trading Systems", "Financial Servers", "Transaction Processing", "Market Data Systems"],
      industries: ["Financial Services", "Banking", "Trading Firms"]
    }
  ];

  // Key features with benefits
  const keyFeatures = [
    {
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      title: "Three-Phase Input Capability",
      description: "Three-phase input with single-phase output provides optimal power distribution and enhanced reliability for demanding environments."
    },
    {
      icon: <Activity className="h-6 w-6 text-blue-600" />,
      title: "ECO Mode Operation",
      description: "Advanced ECO mode reduces energy consumption while maintaining full protection, significantly lowering operational costs."
    },
    {
      icon: <Computer className="h-6 w-6 text-blue-600" />,
      title: "Advanced LCD Interface",
      description: "Comprehensive LCD display provides detailed system information, real-time monitoring, and easy configuration options."
    },
    {
      icon: <Settings className="h-6 w-6 text-blue-600" />,
      title: "Scalable Architecture",
      description: "Designed for future expansion with parallel capability and modular architecture to grow with your business needs."
    }
  ];

  // Industries served
  const industries = [
    "Medium Enterprises", "Data Centers", "Manufacturing", "Healthcare Facilities", 
    "Financial Services", "Industrial Automation", "Process Control", "IT Infrastructure"
  ];

  return (
    <PageLayout
      title="EH-31 Series Overview"
      subtitle="10 kVA & 20 kVA - Enterprise-Grade Power Protection with Three-Phase Input"
      category="protect"
    >
      <div className="font-['Open_Sans']">
        {/* Hero Section */}
        <div className="relative py-12 md:py-16 overflow-hidden bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
              {/* Content */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-6"
              >
                <div className="inline-block bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  KRYKARD EH-31 Series
                </div>
                
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
                  Enterprise-Grade <span className="text-blue-600">Power Protection</span>
                </h1>
                
                <p className="text-lg md:text-xl text-gray-600 dark:text-gray-400 leading-relaxed">
                  The EH-31 Series delivers enterprise-grade power protection for medium-sized businesses with mission-critical applications. Features three-phase input with single-phase output, providing optimal power distribution and enhanced reliability for demanding environments.
                </p>

                <div className="flex flex-wrap gap-4">
                  <Link to="/contact/sales">
                    <motion.button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Mail className="h-5 w-5" />
                      Request Quote
                    </motion.button>
                  </Link>
                  
                  <Link to="/protect/ups/eh-31-series">
                    <motion.button
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg flex items-center gap-2 transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FileText className="h-5 w-5" />
                      View Technical Details
                    </motion.button>
                  </Link>
                </div>
              </motion.div>

              {/* Product Image */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-200/30 to-transparent rounded-full blur-3xl"></div>
                <motion.img
                  src="/UPS/5-removebg-preview.png"
                  alt="EH-31 Series UPS"
                  className="relative z-10 w-full max-w-lg mx-auto drop-shadow-2xl"
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 4,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            </div>
          </div>
        </div>

        {/* Key Features Section */}
        <div className="py-16 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Enterprise Features & Technology
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Advanced three-phase technology designed for mission-critical applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {keyFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start gap-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EH31SeriesOverview;
