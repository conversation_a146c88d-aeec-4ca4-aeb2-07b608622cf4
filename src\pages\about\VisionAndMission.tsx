import React from 'react';
import { Target, Eye } from 'lucide-react';
import PageLayout from '../../components/layout/PageLayout';

export default function AtandraMissionVision() {
  return (
    <PageLayout>
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-12 px-2 relative overflow-hidden">
        {/* Modern background: image + animated gradient overlay */}
        <img
          src="/background_images/vision&mission.png"
          alt="Background"
          className="absolute inset-0 w-full h-full object-cover opacity-15 pointer-events-none z-0 transition-opacity duration-700"
        />
        {/* Animated gradient overlay for modern effect */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="w-2/3 h-2/3 absolute top-0 left-1/4 bg-gradient-to-br from-blue-300/40 via-indigo-200/30 to-green-200/40 rounded-full blur-3xl animate-pulse-slow" />
          <div className="w-1/2 h-1/2 absolute bottom-0 right-0 bg-gradient-to-tr from-green-200/40 via-blue-100/30 to-indigo-200/40 rounded-full blur-2xl animate-pulse-slower" />
        </div>
        
        {/* Title Section */}
        <div className="text-center mb-8 sm:mb-12 relative z-10">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight bg-gradient-to-r from-blue-600 via-indigo-500 to-green-500 bg-clip-text text-transparent drop-shadow-lg">
            Vision and Mission
          </h1>
          <span className="block mx-auto mt-3 h-1 w-24 bg-gradient-to-r from-blue-400 via-indigo-400 to-green-400 rounded-full animate-pulse-slow" />
        </div>

        <div className="w-full max-w-5xl grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 relative z-10">
          {/* Vision Card */}
          <section className="relative bg-white/50 backdrop-blur-xl rounded-3xl shadow-2xl border-0 transition-all duration-300 hover:scale-[1.04] hover:shadow-blue-300/40 group overflow-hidden min-h-[340px]">
            {/* Animated Gradient Border */}
            <div className="absolute inset-0 rounded-3xl pointer-events-none z-0 border-4 border-transparent group-hover:border-blue-400/60" style={{boxShadow:'0 4px 32px 0 rgba(59,130,246,0.08)'}}>
              <div className="absolute inset-0 rounded-3xl border-2 border-blue-400/20 animate-border-glow" />
            </div>
            
            {/* Content with responsive padding */}
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 relative z-10">
              {/* Header with Icon and Title on same line */}
              <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                {/* Icon with Glow */}
                <div className="relative flex-shrink-0">
                  <div className="absolute inset-0 w-full h-full rounded-2xl bg-gradient-to-br from-blue-400/40 to-indigo-400/30 blur-xl opacity-70 animate-pulse" />
                  <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-500 shadow-lg">
                    <Eye className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white drop-shadow-xl" />
                  </div>
                </div>
                
                {/* Title */}
                <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-blue-700 tracking-wide">
                  Our Vision
                </h2>
              </div>

              {/* Content */}
              <ul className="text-slate-700 space-y-2 sm:space-y-3 text-sm sm:text-base leading-relaxed list-disc list-inside">
                <li>To achieve & sustain leadership positions and be the company of choice in all our areas of business</li>
                <li>To be a great place to work where every employee values the environment & opportunity provided</li>
                <li>To be the preferred partner for all our external stakeholders</li>
              </ul>
            </div>

            {/* Subtle pattern */}
            <div className="absolute right-2 bottom-2 w-24 h-24 bg-blue-100/30 rounded-full blur-2xl z-0" />
          </section>

          {/* Mission Card */}
          <section className="relative bg-white/50 backdrop-blur-xl rounded-3xl shadow-2xl border-0 transition-all duration-300 hover:scale-[1.04] hover:shadow-green-300/40 group overflow-hidden min-h-[340px]">
            {/* Animated Gradient Border */}
            <div className="absolute inset-0 rounded-3xl pointer-events-none z-0 border-4 border-transparent group-hover:border-green-400/60" style={{boxShadow:'0 4px 32px 0 rgba(16,185,129,0.08)'}}>
              <div className="absolute inset-0 rounded-3xl border-2 border-green-400/20 animate-border-glow" />
            </div>
            
            {/* Content with responsive padding */}
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 relative z-10">
              {/* Header with Icon and Title on same line */}
              <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                {/* Icon with Glow */}
                <div className="relative flex-shrink-0">
                  <div className="absolute inset-0 w-full h-full rounded-2xl bg-gradient-to-br from-green-400/40 to-emerald-400/30 blur-xl opacity-70 animate-pulse" />
                  <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-green-400 to-emerald-500 shadow-lg">
                    <Target className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 text-white drop-shadow-xl" />
                  </div>
                </div>
                
                {/* Title */}
                <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-green-700 tracking-wide">
                  Our Mission
                </h2>
              </div>

              {/* Content */}
              <p className="text-slate-700 text-sm sm:text-base leading-relaxed">
                To be a respected Indian MNC offering innovative solutions in the domain of power conditioning, measurement & conservation of energy and resources for a more sustainable world by offering best-in-class Products and services with a committed & competent team ensuring continual customer satisfaction with involved partners and Suppliers.
              </p>
            </div>

            {/* Subtle pattern */}
            <div className="absolute left-2 top-2 w-24 h-24 bg-green-100/30 rounded-full blur-2xl z-0" />
          </section>
        </div>
      </div>
    </PageLayout>
  );
}