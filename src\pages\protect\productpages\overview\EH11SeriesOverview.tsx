import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PageLayout from '@/components/layout/PageLayout';
import { 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Building, 
  Hospital, 
  Computer, 
  Factory,
  Monitor,
  Server,
  FileText,
  Mail,
  Battery,
  Gauge
} from 'lucide-react';

const EH11SeriesOverview = () => {
  // Use cases with detailed descriptions
  const useCases = [
    {
      icon: <Building className="h-8 w-8 text-blue-600" />,
      title: "Medium Business Infrastructure",
      description: "Perfect for businesses requiring higher power capacity while maintaining a compact footprint for space-constrained environments. Ideal for protecting multiple workstations and small server rooms.",
      applications: ["Multiple Workstations", "Small Server Rooms", "Network Infrastructure", "Communication Systems"],
      industries: ["Small-Medium Businesses", "Branch Offices", "Regional Centers"]
    },
    {
      icon: <Server className="h-8 w-8 text-blue-600" />,
      title: "Small Server Room Protection",
      description: "Ideal for protecting small server rooms and network infrastructure with advanced monitoring capabilities. Ensures continuous operation of critical IT systems.",
      applications: ["Server Racks", "Network Switches", "Storage Systems", "Backup Systems"],
      industries: ["IT Companies", "Data Processing", "Cloud Services"]
    },
    {
      icon: <Hospital className="h-8 w-8 text-blue-600" />,
      title: "Medical Office Equipment",
      description: "Essential for medical offices requiring reliable power backup for critical medical equipment and patient data systems. Ensures patient care continuity.",
      applications: ["Medical Computers", "Diagnostic Equipment", "Patient Records", "Lab Equipment"],
      industries: ["Medical Offices", "Clinics", "Diagnostic Centers"]
    },
    {
      icon: <Factory className="h-8 w-8 text-blue-600" />,
      title: "Industrial Control Systems",
      description: "Provides reliable power protection for industrial automation and control systems, preventing costly production downtime and equipment damage.",
      applications: ["PLC Systems", "HMI Panels", "Control Computers", "Monitoring Systems"],
      industries: ["Manufacturing", "Process Control", "Automation"]
    }
  ];

  // Key features with benefits
  const keyFeatures = [
    {
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      title: "Advanced DSP Technology",
      description: "Digital Signal Processing technology provides flexible operation and superior power quality control for demanding applications."
    },
    {
      icon: <Battery className="h-6 w-6 text-blue-600" />,
      title: "Advanced Battery Management",
      description: "Intelligent battery management system extends battery life and provides real-time health monitoring with automatic testing."
    },
    {
      icon: <Monitor className="h-6 w-6 text-blue-600" />,
      title: "LCD Display & Monitoring",
      description: "Comprehensive LCD display provides real-time system status, load levels, and operational parameters for easy monitoring."
    },
    {
      icon: <Gauge className="h-6 w-6 text-blue-600" />,
      title: "High Efficiency Design",
      description: "Up to 95% efficiency in offline mode and 93% in online mode, reducing energy costs and environmental impact."
    }
  ];

  // Industries served
  const industries = [
    "Small-Medium Businesses", "Retail Chains", "Medical Offices", "Educational Institutions", 
    "Branch Offices", "IT Companies", "Manufacturing", "Process Control"
  ];

  return (
    <PageLayout
      title="EH-11 Series Overview"
      subtitle="6 kVA & 10 kVA - Compact Design with Robust Power Protection"
      category="protect"
    >
      <div className="font-['Open_Sans']">
        {/* Hero Section - Image Left, Content Right */}
        <div className="relative py-6 md:py-8 overflow-hidden bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-center">
              {/* Product Image - Left Side */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative order-1 lg:order-1"
              >
                <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg">
                  <motion.img
                    src="/UPS/SB4_-_2-removebg-preview.png"
                    alt="EH-11 Series UPS"
                    className="w-full max-w-xs mx-auto drop-shadow-lg"
                    animate={{
                      y: [0, -5, 0],
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 4,
                      ease: "easeInOut"
                    }}
                  />
                </div>
              </motion.div>

              {/* Content - Right Side */}
              <motion.div
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="space-y-4 order-2 lg:order-2 font-['Open_Sans']"
              >
                <div className="inline-block bg-blue-600 text-white px-3 py-1.5 rounded-full text-sm font-semibold font-['Open_Sans']">
                  KRYKARD EH-11 Series
                </div>

                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white leading-tight font-['Open_Sans']">
                  Compact Design with <span className="text-blue-600">Robust Protection</span>
                </h1>

                <p className="text-base md:text-lg text-gray-600 dark:text-gray-400 leading-relaxed font-['Open_Sans']">
                  The EH-11 Series combines compact design with robust power protection capabilities, making it ideal for small to medium-sized businesses requiring reliable backup power. Features advanced battery management and intelligent monitoring systems for optimal performance and longevity.
                </p>

                <div className="flex flex-wrap gap-3">
                  <Link to="/contact/sales">
                    <motion.button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2.5 rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans'] font-semibold"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Mail className="h-4 w-4" />
                      Request Quote
                    </motion.button>
                  </Link>

                  <Link to="/protect/ups/eh-11-series">
                    <motion.button
                      className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-5 py-2.5 rounded-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans'] font-semibold"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <FileText className="h-4 w-4" />
                      View Technical Details
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Key Features Section */}
        <div className="py-6 md:py-8 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Advanced Features & Technology
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Engineered for businesses that need higher power capacity with intelligent monitoring
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {keyFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-900 p-5 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start gap-3">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2.5 rounded-lg">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 font-['Open_Sans']">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed font-['Open_Sans']">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div className="py-6 md:py-8 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Real-World Applications
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Discover how the EH-11 Series provides reliable power protection across various business environments
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {useCases.map((useCase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2.5 rounded-lg">
                      {useCase.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white font-['Open_Sans']">
                      {useCase.title}
                    </h3>
                  </div>

                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4 font-['Open_Sans']">
                    {useCase.description}
                  </p>

                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-['Open_Sans']">
                        Typical Applications:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {useCase.applications.map((app, i) => (
                          <span
                            key={i}
                            className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2.5 py-1 rounded-full text-sm font-medium font-['Open_Sans']"
                          >
                            {app}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-['Open_Sans']">
                        Industries:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {useCase.industries.map((industry, i) => (
                          <span
                            key={i}
                            className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2.5 py-1 rounded-full text-sm font-medium font-['Open_Sans']"
                          >
                            {industry}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Industries Served Section */}
        <div className="py-6 md:py-8 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8 font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Industries We Serve
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto font-['Open_Sans']">
                Trusted by businesses across multiple sectors for reliable power protection
              </p>
            </motion.div>

            <div className="flex flex-wrap justify-center gap-3">
              {industries.map((industry, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: index * 0.05 }}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 font-['Open_Sans']"
                >
                  {industry}
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="py-8 md:py-10 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="font-['Open_Sans']"
            >
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3 font-['Open_Sans']">
                Ready to Upgrade Your Power Protection?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto font-['Open_Sans']">
                Get expert consultation and find the perfect EH-11 Series UPS for your business needs
              </p>

              <div className="flex flex-wrap justify-center gap-3">
                <Link to="/contact/sales">
                  <motion.button
                    className="bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 rounded-lg font-bold shadow-lg flex items-center gap-2 transition-all duration-300 font-['Open_Sans']"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Mail className="h-4 w-4" />
                    Get Quote Now
                  </motion.button>
                </Link>

                <Link to="/protect/ups/eh-11-series">
                  <motion.button
                    className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-3 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 font-['Open_Sans']"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FileText className="h-4 w-4" />
                    View Technical Specs
                    <ArrowRight className="h-4 w-4" />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EH11SeriesOverview;
