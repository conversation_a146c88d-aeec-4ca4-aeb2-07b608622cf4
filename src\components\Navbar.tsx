import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ChevronDown,
  Menu,
  X,
  Phone,
  Mail,
  ArrowUpRight,
  Zap,
  Shield,
  Leaf,
  MessageCircle,
  Building2,
  ChevronRight
} from "lucide-react";
import ProfessionalFooter from '@/components/layout/FooterComponents';

// Custom hook for responsive breakpoints
const useResponsiveBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

// Colorful Top Border Component for Navigation
const ColorfulTopBorder = () => (
  <div className="fixed top-0 left-0 w-full h-1 sm:h-1.5 md:h-2 flex z-[120]">
    <div className="flex-1" style={{ backgroundColor: '#FFD700' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#FF4500' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#000000' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#808080' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#008000' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#00BFFF' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#FFD700' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#800000' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#FF4500' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#000000' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#808080' }}></div>
    <div className="flex-1" style={{ backgroundColor: '#008000' }}></div>
  </div>
);

// Modern Attractive Dropdown Design
const MegaDropdown = ({
  items,
  isOpen,
  color,
  category,
  onItemClick
}: {
  items: { name: string; path: string }[],
  isOpen: boolean,
  color: string,
  category: string,
  onItemClick?: () => void
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const breakpoint = useResponsiveBreakpoint();

  useEffect(() => {
    if (isOpen) {
      setShouldShow(true);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    } else if (!isHovered) {
      timeoutRef.current = setTimeout(() => {
        setShouldShow(false);
      }, 150);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isOpen, isHovered]);

  if (!shouldShow || breakpoint === 'mobile') return null;

  const getColorConfig = () => {
    switch (color) {
      case 'measure':
        return {
          bg: 'bg-yellow-500',
          lightBg: 'bg-yellow-50',
          text: 'text-yellow-600',
          border: 'border-yellow-200',
          hover: 'hover:bg-yellow-50'
        };
      case 'protect':
        return {
          bg: 'bg-blue-500',
          lightBg: 'bg-blue-50',
          text: 'text-blue-600',
          border: 'border-blue-200',
          hover: 'hover:bg-blue-50'
        };
      case 'conserve':
        return {
          bg: 'bg-emerald-500',
          lightBg: 'bg-emerald-50',
          text: 'text-emerald-600',
          border: 'border-emerald-200',
          hover: 'hover:bg-emerald-50'
        };
      case 'about':
        return {
          bg: 'bg-purple-500',
          lightBg: 'bg-purple-50',
          text: 'text-purple-600',
          border: 'border-purple-200',
          hover: 'hover:bg-purple-50'
        };
      case 'contact':
        return {
          bg: 'bg-gray-500',
          lightBg: 'bg-gray-50',
          text: 'text-gray-600',
          border: 'border-gray-200',
          hover: 'hover:bg-gray-50'
        };
      default:
        return {
          bg: 'bg-gray-500',
          lightBg: 'bg-gray-50',
          text: 'text-gray-600',
          border: 'border-gray-200',
          hover: 'hover:bg-gray-50'
        };
    }
  };

  const colorConfig = getColorConfig();
  const isMeasureCategory = category === 'measure';

  return (
    <motion.div
      className={cn(
        "absolute top-full left-1/2 transform -translate-x-1/2 mt-2",
        "bg-white border border-gray-200 rounded-2xl shadow-xl",
        "overflow-hidden z-[120]",
        isMeasureCategory ? "w-[480px]" : "w-[320px]"
      )}
      initial={{ opacity: 0, y: -10, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -10, scale: 0.95 }}
      transition={{ duration: 0.2, ease: [0.23, 1, 0.32, 1] }}
      onMouseEnter={() => {
        setIsHovered(true);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      }}
      onMouseLeave={() => {
        setIsHovered(false);
      }}
    >
      {/* Clean Header */}
      <div className={cn("px-4 py-3 border-b", colorConfig.lightBg, colorConfig.border)}>
        <h3 className={cn("font-bold text-base", colorConfig.text)}>
          {category.charAt(0).toUpperCase() + category.slice(1)}
        </h3>
        {(category === 'measure' || category === 'protect') && (
          <p className="text-xs text-gray-500 mt-0.5">Choose your product</p>
        )}
      </div>

      {/* Menu Items */}
      <div className="py-2">
        <div className={isMeasureCategory ? "grid grid-cols-2" : ""}>
          {items.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.1, delay: index * 0.02 }}
            >
              <Link
                to={item.path}
                className={cn(
                  "flex items-center justify-between px-4 py-3",
                  "text-gray-700 hover:text-gray-900",
                  "transition-all duration-200 group",
                  colorConfig.hover,
                  "border-b border-gray-50 last:border-b-0"
                )}
                onClick={onItemClick}
              >
                <span className="text-sm font-medium flex-1 pr-2">
                  {item.name}
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200" />
              </Link>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Footer Button */}
      <div className="p-3 border-t border-gray-100">
        <Link
          to={`/${category}`}
          className={cn(
            "w-full flex items-center justify-center gap-2 px-4 py-2.5",
            "rounded-xl text-white font-semibold text-sm",
            "transition-all duration-200 hover:shadow-md",
            "transform hover:scale-105 active:scale-95",
            colorConfig.bg
          )}
          onClick={onItemClick}
        >
          <span>View All Products</span>
          <ArrowUpRight className="w-4 h-4" />
        </Link>
      </div>
    </motion.div>
  );
};

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [scrollY, setScrollY] = useState(0);
  const breakpoint = useResponsiveBreakpoint();

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isScrolled = scrollY > 20;

  // Close mobile menu when resizing to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false);
        setActiveDropdown(null);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close dropdown when switching to mobile
  useEffect(() => {
    if (breakpoint === 'mobile') {
      setActiveDropdown(null);
    }
  }, [breakpoint]);

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMobileMenuOpen]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMouseEnter = (dropdown: string) => {
    if (breakpoint !== 'mobile') {
      setActiveDropdown(dropdown);
    }
  };

  const handleMouseLeave = () => {
    if (breakpoint !== 'mobile') {
      setActiveDropdown(null);
    }
  };

  // Enhanced menu categories with modern structure
  const menuCategories = {
    measure: {
      name: "Measure",
      icon: Zap,
      items: [
        { name: "Power Quality Analyzers", path: "/measure/power-quality-analyzers" },
        { name: "Thermal Imagers", path: "/measure/thermal-imagers" },
        { name: "Insulation Testers", path: "/measure/insulation-testers" },
        { name: "Oscilloscopes", path: "/measure/oscilloscopes" },
        { name: "Earth Testers", path: "/measure/EarthTesters" },
        { name: "Earth Loop Testers", path: "/measure/earth-loop-testers" },
        { name: "Clamp Meters", path: "/measure/clamp-meters" },
        { name: "Digital Multimeters", path: "/measure/digital-multimeters" },
        { name: "Micro Ohm Meters", path: "/measure/micro-ohmmeters" },
        { name: "Installation Testers", path: "/measure/installation-testers" },
        { name: "Multi Functional Meters", path: "/measure/multi-functional-meters" },
      ]
    },
    protect: {
      name: "Protect",
      icon: Shield,
      items: [
        { name: "Online UPS", path: "/protect/ups" },
        { name: "Servo Stabilizers", path: "/protect/servo-stabilizers" },
        { name: "Static Stabilizers", path: "/protect/static-stabilizers" },
        { name: "Isolation Transformers", path: "/protect/isolation-transformers" },
      ]
    },
    conserve: {
      name: "Conserve",
      icon: Leaf,
      items: [
        { name: "Smart Energy Management System", path: "/conserve/on-premise-systems" },
        { name: "Smart Factory Solution", path: "/conserve/smart-factory-solution" },
        { name: "Tenant Billing Solution", path: "/conserve/tenant-billing-solution" },
        { name: "Enterprise ESG Reporting", path: "/conserve/enterprise-esg-reporting" },
      ]
    },
    about: {
      name: "About Us",
      icon: Building2,
      items: [
        { name: "Company", path: "/about/company" },
        { name: "Vision & Mission", path: "/about/vision-mission" },
        { name: "Infrastructure & Our Network", path: "/about/infrastructure-network" },
        { name: "Certificates", path: "/about/certificates" },
        { name: "Events", path: "/about/events" },
      ]
    },
    contact: {
      name: "Contact",
      icon: MessageCircle,
      items: [
        { name: "Contact Sales", path: "/contact/sales" },
        { name: "Technical Services", path: "/contact/service" },
      ]
    }
  };

  return (
    <>
      {/* Colorful Top Border */}
      <ColorfulTopBorder />

      {/* Main Header */}
      <header
        className={cn(
          "fixed left-0 right-0 z-[100] transition-all duration-500 font-['Open_Sans'] bg-white/95 shadow-lg rounded-b-2xl border-b border-gray-100/50",
          isScrolled ? "" : ""
        )}
        style={{
          top: '4px',
          minHeight: '48px',
          boxShadow: '0 2px 16px 0 rgba(0,0,0,0.06)',
        }}
      >
        <div className="w-full max-w-[1400px] mx-auto px-2 sm:px-4 lg:px-8">
          <nav className="flex items-center justify-between py-2 sm:py-4 lg:py-3 min-h-[48px] sm:min-h-[70px]">
            {/* Logo - Left */}
            <div className="flex items-center">
              <Link to="/" className="block">
                <img
                  src="/unnamed.png"
                  alt="KRYKARD"
                  className="object-contain filter drop-shadow-sm h-12 lg:h-16 w-auto"
                />
              </Link>
            </div>

            {/* Hamburger Menu - Right Corner (Mobile Only) */}
            <motion.button
              className="lg:hidden flex items-center justify-center p-3 rounded-xl bg-blue-50 border border-blue-200 hover:bg-blue-100 transition-all duration-200"
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
              aria-expanded={isMobileMenuOpen}
              whileTap={{ scale: 0.95 }}
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="w-6 h-6 text-blue-600" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="w-6 h-6 text-blue-600" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>

            {/* Desktop Navigation - Right side */}
            <div className="hidden lg:flex items-center space-x-2 xl:space-x-3 ml-auto">
              {Object.entries(menuCategories).map(([key, category]) => (
                <div
                  key={key}
                  className="relative"
                  onMouseEnter={() => handleMouseEnter(key)}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    to={`/${key}`}
                    className={cn(
                      "group flex items-center font-semibold text-sm xl:text-base transition-all duration-200 relative px-4 py-2.5 rounded-xl",
                      "hover:shadow-lg hover:shadow-black/5 backdrop-blur-sm",
                      "border border-transparent hover:border-gray-200",
                      "bg-white/60 hover:bg-white/80",
                      key === 'measure' ? 'text-yellow-600 hover:text-yellow-700' :
                      key === 'protect' ? 'text-blue-700 hover:text-blue-800' :
                      key === 'conserve' ? 'text-emerald-700 hover:text-emerald-800' :
                      key === 'about' ? 'text-purple-700 hover:text-purple-800' :
                      key === 'contact' ? 'text-blue-700 hover:text-blue-800' :
                      'text-gray-700 hover:text-gray-900'
                    )}
                  >
                    <span className="relative z-10">{category.name}</span>
                    <ChevronDown className={cn(
                      "ml-2 h-4 w-4 transition-transform duration-200",
                      activeDropdown === key ? "rotate-180" : ""
                    )} />
                  </Link>

                  <MegaDropdown
                    items={category.items}
                    isOpen={activeDropdown === key}
                    color={key}
                    category={key}
                  />
                </div>
              ))}
            </div>

            {/* Contact Info */}
            <div className="hidden xl:flex flex-col items-start ml-8 space-y-2">
              <motion.div
                className="flex items-center group cursor-pointer"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="p-2.5 bg-blue-500 rounded-xl mr-3 shadow-sm flex-shrink-0">
                  <Phone className="h-4 w-4 text-white" />
                </div>
                <a
                  href="tel:+919500097966"
                  className="text-blue-600 hover:text-blue-700 transition-colors font-medium text-sm group-hover:underline whitespace-nowrap"
                >
                  +91 95000 97966
                </a>
              </motion.div>
              <motion.div
                className="flex items-center group cursor-pointer"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="p-2.5 bg-blue-500 rounded-xl mr-3 shadow-sm flex-shrink-0">
                  <Mail className="h-4 w-4 text-white" />
                </div>
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-700 hover:text-gray-800 transition-colors font-medium text-sm group-hover:underline whitespace-nowrap"
                >
                  <EMAIL>
                </a>
              </motion.div>
            </div>
          </nav>
        </div>
      </header>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 z-[90] bg-black/20 lg:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Mobile Menu Panel */}
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              className="fixed right-0 top-0 bottom-0 z-[95] w-full max-w-sm bg-white lg:hidden overflow-y-auto shadow-2xl"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-100">
                <Link to="/" onClick={() => setIsMobileMenuOpen(false)}>
                  <img src="/unnamed.png" alt="KRYKARD" className="h-10 w-auto" />
                </Link>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-700" />
                </button>
              </div>

              {/* Navigation */}
              <nav className="p-4">
                {Object.entries(menuCategories).map(([key, category]) => (
                  <div key={key} className="mb-6">
                    <Link
                      to={`/${key}`}
                      className={cn(
                        "block font-bold text-lg mb-3 pb-2 border-b-2 transition-colors",
                        key === 'measure' ? 'text-yellow-600 border-yellow-200' :
                        key === 'protect' ? 'text-blue-700 border-blue-200' :
                        key === 'conserve' ? 'text-emerald-700 border-emerald-200' :
                        key === 'about' ? 'text-purple-700 border-purple-200' :
                        key === 'contact' ? 'text-blue-700 border-blue-200' :
                        'text-gray-700 border-gray-200'
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {category.name}
                    </Link>
                    <div className="space-y-2 ml-2">
                      {category.items.map((item, idx) => (
                        <Link
                          key={idx}
                          to={item.path}
                          className="block text-gray-600 hover:text-gray-900 transition-colors py-1 text-sm"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </nav>

              {/* Contact Footer */}
              <div className="p-4 border-t border-gray-100 mt-auto">
                <div className="space-y-3">
                  <a
                    href="tel:+919500097966"
                    className="flex items-center gap-3 text-blue-600 hover:text-blue-700 transition-colors"
                  >
                    <Phone className="w-5 h-5" />
                    <span className="font-medium">+91 95000 97966</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center gap-3 text-gray-600 hover:text-gray-700 transition-colors"
                  >
                    <Mail className="w-5 h-5" />
                    <span className="font-medium"><EMAIL></span>
                  </a>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Footer Component */}
      <ProfessionalFooter />
    </>
  );
};

export default Navbar;