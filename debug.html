<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - React App Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 React App Debug Tool</h1>
        
        <div class="test-section">
            <h3>1. Basic Environment Check</h3>
            <div id="env-check" class="status info">Checking environment...</div>
            <pre id="env-details"></pre>
        </div>

        <div class="test-section">
            <h3>2. Network Connectivity</h3>
            <div id="network-check" class="status info">Testing network...</div>
            <button onclick="testNetwork()">Test Network</button>
        </div>

        <div class="test-section">
            <h3>3. React App Resources</h3>
            <div id="react-check" class="status info">Checking React resources...</div>
            <button onclick="testReactResources()">Test React Resources</button>
            <pre id="react-details"></pre>
        </div>

        <div class="test-section">
            <h3>4. Console Errors</h3>
            <div id="console-errors" class="status info">Monitoring console...</div>
            <pre id="error-log"></pre>
        </div>

        <div class="test-section">
            <h3>5. Manual Tests</h3>
            <button onclick="openReactApp()">Open React App in New Tab</button>
            <button onclick="checkDevTools()">Check Dev Tools</button>
            <button onclick="clearCache()">Clear Cache & Reload</button>
        </div>
    </div>

    <script>
        let errorLog = [];
        
        // Capture console errors
        const originalError = console.error;
        console.error = function(...args) {
            errorLog.push(new Date().toISOString() + ': ' + args.join(' '));
            updateErrorLog();
            originalError.apply(console, args);
        };

        // Capture window errors
        window.addEventListener('error', function(e) {
            errorLog.push(new Date().toISOString() + ': ' + e.message + ' at ' + e.filename + ':' + e.lineno);
            updateErrorLog();
        });

        function updateErrorLog() {
            document.getElementById('error-log').textContent = errorLog.join('\n') || 'No errors detected';
            document.getElementById('console-errors').className = errorLog.length > 0 ? 'status error' : 'status success';
            document.getElementById('console-errors').textContent = errorLog.length > 0 ? 
                `❌ ${errorLog.length} error(s) detected` : '✅ No console errors';
        }

        // Environment check
        function checkEnvironment() {
            const envDiv = document.getElementById('env-check');
            const detailsDiv = document.getElementById('env-details');
            
            const details = {
                'Current URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Screen Size': `${screen.width}x${screen.height}`,
                'Window Size': `${window.innerWidth}x${window.innerHeight}`,
                'Local Storage Available': typeof(Storage) !== "undefined",
                'Session Storage Available': typeof(sessionStorage) !== "undefined",
                'Cookies Enabled': navigator.cookieEnabled,
                'Online Status': navigator.onLine,
                'Language': navigator.language,
                'Platform': navigator.platform,
                'Referrer': document.referrer || 'Direct access'
            };
            
            detailsDiv.textContent = JSON.stringify(details, null, 2);
            envDiv.className = 'status success';
            envDiv.textContent = '✅ Environment check complete';
        }

        // Network test
        async function testNetwork() {
            const networkDiv = document.getElementById('network-check');
            networkDiv.className = 'status info';
            networkDiv.textContent = '⏳ Testing network connectivity...';
            
            try {
                // Test main React app
                const reactResponse = await fetch('http://localhost:8081/', { method: 'HEAD' });
                
                // Test backend
                const backendResponse = await fetch('http://localhost:5007/health');
                
                if (reactResponse.ok && backendResponse.ok) {
                    networkDiv.className = 'status success';
                    networkDiv.textContent = '✅ Network connectivity OK';
                } else {
                    networkDiv.className = 'status warning';
                    networkDiv.textContent = `⚠️ Partial connectivity - React: ${reactResponse.status}, Backend: ${backendResponse.status}`;
                }
            } catch (error) {
                networkDiv.className = 'status error';
                networkDiv.textContent = '❌ Network connectivity failed: ' + error.message;
            }
        }

        // Test React resources
        async function testReactResources() {
            const reactDiv = document.getElementById('react-check');
            const detailsDiv = document.getElementById('react-details');
            
            reactDiv.className = 'status info';
            reactDiv.textContent = '⏳ Testing React resources...';
            
            const resources = [
                'http://localhost:8081/',
                'http://localhost:8081/src/main.tsx',
                'http://localhost:8081/src/App.tsx',
                'http://localhost:8081/src/index.css',
                'http://localhost:8081/index.html'
            ];
            
            const results = {};
            
            for (const resource of resources) {
                try {
                    const response = await fetch(resource, { method: 'HEAD' });
                    results[resource] = `${response.status} ${response.statusText}`;
                } catch (error) {
                    results[resource] = `ERROR: ${error.message}`;
                }
            }
            
            detailsDiv.textContent = JSON.stringify(results, null, 2);
            
            const hasErrors = Object.values(results).some(result => result.includes('ERROR') || !result.startsWith('2'));
            reactDiv.className = hasErrors ? 'status error' : 'status success';
            reactDiv.textContent = hasErrors ? '❌ React resources have issues' : '✅ React resources OK';
        }

        // Manual test functions
        function openReactApp() {
            window.open('http://localhost:8081', '_blank');
        }

        function checkDevTools() {
            alert('Please open Developer Tools (F12) and check the Console and Network tabs for errors');
        }

        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            location.reload(true);
        }

        // Run initial checks
        checkEnvironment();
        testNetwork();
        updateErrorLog();
        
        // Auto-refresh error log every 2 seconds
        setInterval(updateErrorLog, 2000);
    </script>
</body>
</html>
