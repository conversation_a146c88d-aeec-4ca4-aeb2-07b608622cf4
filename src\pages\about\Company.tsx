"use client";

"use client";

import React, { useState, useEffect } from 'react';
import { Award, Users, Shield, Zap, Building, Globe, CheckCircle, Star, ArrowRight, Phone, Mail, Trophy, Target, Rocket, HandHeart } from 'lucide-react';
import PageLayout from '../../components/layout/PageLayout';

const Company = () => {
  // Define the type for isVisible keys
  const sectionKeys = [
    'hero',
    'content',
    'facilities',
    'resources',
    'cta',
  ] as const;
  type SectionKey = typeof sectionKeys[number];

  const [isVisible, setIsVisible] = useState<Record<SectionKey, boolean>>({
    hero: false,
    content: false,
    facilities: false,
    resources: false,
    cta: false,
  });

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.target.id && sectionKeys.includes(entry.target.id as SectionKey)) {
            setIsVisible(prev => ({
              ...prev,
              [entry.target.id as SectionKey]: true
            }));
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('[data-aos]');
    elements.forEach((el) => {
      if (el.getAttribute('data-aos')) {
        el.id = el.getAttribute('data-aos');
        observer.observe(el);
      }
    });

    return () => observer.disconnect();
  }, []);

  const statsData = [
    {
      icon: Trophy,
      title: "INDIA'S NO.1",
      subtitle: "MANUFACTURER OF",
      highlight: "SERVO STABILIZERS",
      bgColor: "from-amber-400 to-orange-500"
    },
    {
      icon: Users,
      number: "100+",
      title: "SERVICE CENTERS",
      bgColor: "from-blue-400 to-indigo-500"
    },
    {
      icon: HandHeart,
      title: "PREFERRED SUPPLIER OF",
      subtitle: "LARGE CORPORATES & OEMs",
      bgColor: "from-emerald-400 to-cyan-500"
    },
    {
      icon: Shield,
      title: "CE CERTIFIED PRODUCTS",
      bgColor: "from-slate-400 to-zinc-500"
    },
    {
      icon: Award,
      number: "40+",
      title: "YEARS EXPERIENCE",
      bgColor: "from-pink-400 to-red-500"
    }
  ];

  return (
    <PageLayout 
      title="ABOUT US"
      subtitle="Discover the power behind India's leading energy solutions"
      category="about"
    >
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 font-sans">
        {/* Simple Hero Section */}
        <section 
          data-aos="hero"
          className={`py-12 sm:py-16 px-2 sm:px-6 bg-white transition-all duration-1000 ${isVisible.hero ? 'opacity-100' : 'opacity-0'}`}
        >
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-3xl sm:text-6xl font-black mb-4 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent">
              ABOUT US
            </h1>
            <p className="text-base sm:text-xl text-gray-600 max-w-2xl mx-auto">
              Discover the power behind India's leading energy solutions
            </p>
          </div>
        </section>

        {/* Main Content Section */}
        <section 
          data-aos="content"
          className={`py-8 sm:py-12 px-2 sm:px-6 transition-all duration-1000 ${isVisible.content ? 'opacity-100' : 'opacity-0'}`}
        >
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
              {/* Left Content */}
              <div className="bg-white rounded-2xl p-4 sm:p-8 shadow-lg">
                <div className="flex items-center mb-4 sm:mb-6">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mr-3 sm:mr-4 shadow-lg flex-shrink-0">
                    <Building className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <h2 className="text-xl sm:text-3xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Our Foundation</h2>
                </div>
                <div className="space-y-4 sm:space-y-6">
                  {/* Company Heritage */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 sm:p-6 border-l-4 border-blue-500 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-14 h-14 sm:w-20 sm:h-20 bg-blue-200 rounded-full opacity-20 transform translate-x-6 sm:translate-x-10 -translate-y-6 sm:-translate-y-10"></div>
                    <div className="relative z-10">
                      <div className="flex items-center mb-2 sm:mb-3">
                        <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                          <Award className="w-4 h-4 text-white" />
                        </div>
                        <h3 className="text-base sm:text-lg font-bold text-blue-700">Company Heritage</h3>
                      </div>
                      <p className="text-gray-700 text-sm sm:text-lg leading-relaxed">
                        Atandra Energy Pvt. Ltd., headquartered in <span className="font-bold text-blue-600 bg-blue-100 px-2 py-1 rounded">Chennai</span>, draws upon a rich foundation of more than 
                        <span className="font-black text-blue-600 bg-blue-100 px-2 sm:px-3 py-1 sm:py-2 rounded-full mx-1 sm:mx-2 text-base sm:text-xl shadow-sm">40+ years of expertise</span>
                        in the realm of Power & Energy Management.
                      </p>
                    </div>
                  </div>
                  {/* Brand Excellence */}
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-4 sm:p-6 border-l-4 border-purple-500 relative overflow-hidden">
                    <div className="absolute bottom-0 left-0 w-10 h-10 sm:w-16 sm:h-16 bg-purple-200 rounded-full opacity-20 transform -translate-x-4 sm:-translate-x-8 translate-y-4 sm:translate-y-8"></div>
                    <div className="relative z-10">
                      <div className="flex items-center mb-2 sm:mb-3">
                        <div className="w-7 h-7 sm:w-8 sm:h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                          <Star className="w-4 h-4 text-white" />
                        </div>
                        <h3 className="text-base sm:text-lg font-bold text-purple-700">KRYKARD Brand Excellence</h3>
                      </div>
                      <p className="text-gray-700 leading-relaxed mb-2 sm:mb-4">
                        We offer solutions to industrial and commercial establishments under our prestigious brand 
                        <span className="font-black text-purple-600 bg-purple-100 px-2 sm:px-3 py-1 sm:py-2 rounded-full mx-1 sm:mx-2 text-base sm:text-lg shadow-sm">KRYKARD</span>
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                        <div className="bg-white rounded-lg p-2 sm:p-3 border border-purple-200">
                          <div className="text-xl sm:text-2xl font-black text-green-600">5,00,000+</div>
                          <div className="text-xs sm:text-sm font-semibold text-gray-600">Power Conditioner Installations</div>
                        </div>
                        <div className="bg-white rounded-lg p-2 sm:p-3 border border-purple-200">
                          <div className="text-xl sm:text-2xl font-black text-blue-600">1,50,000+</div>
                          <div className="text-xs sm:text-sm font-semibold text-gray-600">Load Manager Installations</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Quality Standards */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-4 sm:p-6 border-l-4 border-green-500 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 bg-green-200 rounded-full opacity-20 transform translate-x-8 sm:translate-x-12 -translate-y-8 sm:-translate-y-12"></div>
                    <div className="relative z-10">
                      <div className="flex items-center mb-2 sm:mb-3">
                        <div className="w-7 h-7 sm:w-8 sm:h-8 bg-green-500 rounded-lg flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                          <Shield className="w-4 h-4 text-white" />
                        </div>
                        <h3 className="text-base sm:text-lg font-bold text-green-700">Quality Assurance</h3>
                      </div>
                      <p className="text-gray-700 leading-relaxed mb-2 sm:mb-3">
                        Our Servo Stabilizers and Transformers have obtained comprehensive certifications, providing our customers with the assurance that these products adhere to rigorous global standards.
                      </p>
                      <div className="flex flex-wrap gap-1 sm:gap-2">
                        <span className="bg-white text-green-700 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold border border-green-200">CE Certification</span>
                        <span className="bg-white text-green-700 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold border border-green-200">ISO Standards</span>
                        <span className="bg-white text-green-700 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold border border-green-200">Global Compliance</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Right Stats Panel */}
              <div className="bg-white rounded-2xl p-4 sm:p-8 shadow-lg mt-6 lg:mt-0">
                <div className="space-y-4 sm:space-y-6">
                  {statsData.map((stat, index) => {
                    const IconComponent = stat.icon;
                    return (
                      <div key={index} className="group">
                        {/* India's No.1 - Special Design */}
                        {stat.title === "INDIA'S NO.1" && (
                          <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-2xl p-4 sm:p-6 border border-orange-200">
                            <div className="flex items-center space-x-3 sm:space-x-4">
                              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-orange-400 to-amber-500 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-lg sm:text-xl font-black text-orange-600 mb-1">{stat.title}</div>
                                <div className="text-xs sm:text-sm font-semibold text-gray-600 mb-1">{stat.subtitle}</div>
                                <div className="text-base sm:text-lg font-bold text-blue-600 bg-blue-100 px-2 sm:px-3 py-1 rounded-lg inline-block">
                                  {stat.highlight}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* Service Centers - Number Highlight */}
                        {stat.title === "SERVICE CENTERS" && (
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 sm:p-6 border border-blue-200">
                            <div className="flex items-center space-x-3 sm:space-x-4">
                              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-2xl sm:text-4xl font-black text-blue-600 mb-1">{stat.number}</div>
                                <div className="text-base sm:text-lg font-bold text-black">{stat.title}</div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* Preferred Supplier - Two Line Design */}
                        {stat.title === "PREFERRED SUPPLIER OF" && (
                          <div className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-2xl p-4 sm:p-6 border border-teal-200">
                            <div className="flex items-center space-x-3 sm:space-x-4">
                              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-base sm:text-lg font-black text-teal-700 mb-1">{stat.title}</div>
                                <div className="text-xs sm:text-base font-semibold text-gray-600">{stat.subtitle}</div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* CE Certified - Simple Clean */}
                        {stat.title === "CE CERTIFIED PRODUCTS" && (
                          <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-4 sm:p-6 border border-gray-200">
                            <div className="flex items-center space-x-3 sm:space-x-4">
                              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-slate-500 to-gray-600 rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-base sm:text-lg font-black text-black">{stat.title}</div>
                                <div className="text-xs sm:text-sm text-gray-500 mt-1">Global Standards Compliance</div>
                              </div>
                            </div>
                          </div>
                        )}
                        {/* Years Experience - Circular Number */}
                        {stat.title === "YEARS EXPERIENCE" && (
                          <div className="bg-gradient-to-r from-pink-50 to-rose-50 rounded-2xl p-4 sm:p-6 border border-pink-200">
                            <div className="flex items-center space-x-3 sm:space-x-4">
                              <div className="relative w-12 h-12 sm:w-16 sm:h-16 flex-shrink-0">
                                {/* Circular Background */}
                                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-pink-500 to-rose-600 rounded-full flex items-center justify-center shadow-lg">
                                  <div className="text-lg sm:text-2xl font-black text-white">{stat.number}</div>
                                </div>
                                {/* Small Icon */}
                                <div className="absolute -top-2 -right-2 w-6 h-6 sm:w-8 sm:h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                                  <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 text-gray-800" />
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-base sm:text-lg font-black text-pink-700">{stat.title}</div>
                                <div className="text-xs sm:text-sm text-gray-500 mt-1">Legacy of Excellence</div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Facilities Section */}
        <section 
          data-aos="facilities"
          className={`py-12 px-4 sm:px-6 transition-all duration-1000 ${isVisible.facilities ? 'opacity-100' : 'opacity-0'}`}
        >
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-2xl p-6 sm:p-8 shadow-lg">
              <div className="mb-6">
                <h2 className="text-2xl sm:text-3xl font-black text-black">Our Facilities</h2>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-4 sm:p-6 rounded-xl border border-blue-200">
                  <div>
                    <h4 className="font-bold text-black text-lg mb-2">R&D Department</h4>
                    <p className="text-gray-600">Advanced Power Electronics and Electro-magnetics research with cutting-edge innovation labs</p>
                  </div>
                </div>
                
                <div className="bg-teal-50 p-4 sm:p-6 rounded-xl border border-teal-200">
                  <div>
                    <h4 className="font-bold text-black text-lg mb-2">Software Development</h4>
                    <p className="text-gray-600">Energy management software and Industry 4.0 solutions with AI-powered analytics</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Company Resources Section */}
        <section 
          data-aos="resources"
          className={`py-12 px-4 sm:px-6 transition-all duration-1000 ${isVisible.resources ? 'opacity-100' : 'opacity-0'}`}
        >
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl sm:text-4xl font-black text-black mb-3">
                Our Resources & Capabilities
              </h2>
              <p className="text-base sm:text-lg text-gray-600">
                Empowering excellence through world-class infrastructure and expertise
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Manufacturing Excellence */}
              <div className="bg-white rounded-2xl p-6 shadow-lg max-w-md mx-auto">
      <div className="flex items-center mb-4">
        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
          <Building className="w-6 h-6 text-white" />
        </div>
        <h3 className="text-lg sm:text-xl font-bold text-black">Manufacturing Hub</h3>
      </div>
      <div className="space-y-3">
        <div className="flex items-center space-x-3 bg-blue-50 p-3 rounded-lg">
          <CheckCircle className="w-5 h-5 text-blue-500 flex-shrink-0 min-w-4" />
          <span className="text-gray-700 text-sm sm:text-base leading-5 flex-1">State-of-the-art production facilities</span>
        </div>
        <div className="flex items-center space-x-3 bg-blue-50 p-3 rounded-lg">
          <CheckCircle className="w-5 h-5 text-blue-500 flex-shrink-0 min-w-4" />
          <span className="text-gray-700 text-sm sm:text-base leading-5 flex-1">Advanced testing laboratories</span>
        </div>
        <div className="flex items-center space-x-3 bg-blue-50 p-3 rounded-lg">
          <CheckCircle className="w-5 h-5 text-blue-500 flex-shrink-0 min-w-4" />
          <span className="text-gray-700 text-sm sm:text-base leading-5 flex-1">Quality control systems</span>
        </div>
      </div>
    </div>
              {/* Human Resources */}
             <div className="bg-white rounded-2xl p-6 shadow-lg max-w-md mx-auto">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold text-black">Expert Team</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 bg-purple-50 p-3 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-purple-500 flex-shrink-0" />
                    <span className="text-gray-700 text-sm sm:text-base leading-5 flex-1">450+ skilled professionals</span>
                  </div>
                  <div className="flex items-center space-x-3 bg-purple-50 p-3 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-purple-500 flex-shrink-0" />
                    <span className="text-gray-700 text-sm sm:text-base leading-5 flex-1">Expert engineers & technicians</span>
                  </div>
                  <div className="flex items-center space-x-3 bg-purple-50 p-3 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-purple-500 flex-shrink-0" />
                    <span className="text-gray-700 text-sm sm:text-base leading-5 flex-1">Dedicated R&D specialists</span>
                  </div>
                </div>
              </div>

              {/* Service Network */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold text-gray-800">Service Network</h3>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 bg-teal-50 p-3 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-teal-500 flex-shrink-0" />
                    <span className="text-gray-700 text-sm sm:text-base">100+ service centers</span>
                  </div>
                  <div className="flex items-center space-x-3 bg-teal-50 p-3 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-teal-500 flex-shrink-0" />
                    <span className="text-gray-700 text-sm sm:text-base">24/7 technical support</span>
                  </div>
                  <div className="flex items-center space-x-3 bg-teal-50 p-3 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-teal-500 flex-shrink-0" />
                    <span className="text-gray-700 text-sm sm:text-base">Pan-India coverage</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Simple CTA Section */}
        <section 
          data-aos="cta"
          className={`py-12 px-4 sm:px-6 bg-gradient-to-r from-blue-50 to-purple-50 transition-all duration-1000 ${isVisible.cta ? 'opacity-100' : 'opacity-0'}`}
        >
          <div className="max-w-4xl mx-auto text-center">
            <Zap className="w-12 h-12 mx-auto mb-4 text-yellow-400" />
            <h2 className="text-2xl sm:text-4xl font-black mb-4 text-black">Ready to Power Your Success?</h2>
            <p className="text-base sm:text-lg mb-8 text-gray-600 max-w-2xl mx-auto">
              Experience the difference with KRYKARD - India's most trusted power solutions.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-100 text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-blue-200 transition-all flex items-center justify-center">
                <Phone className="w-5 h-5 mr-2 flex-shrink-0" />
                <span>Connect with Experts</span>
              </button>
              <button className="bg-yellow-100 text-yellow-800 px-6 py-3 rounded-lg font-bold hover:bg-yellow-200 transition-all flex items-center justify-center">
                <Mail className="w-5 h-5 mr-2 flex-shrink-0" />
                <span>Get Custom Quote</span>
                <ArrowRight className="w-5 h-5 ml-2 flex-shrink-0" />
              </button>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
}

export default Company;